---
title: '概述'
description: 'DroidRun是一个强大的框架，使您能够通过LLM代理控制Android设备。
它提供了一种简单直观的方式，使用自然语言命令自动化Android设备交互。'
---

<CardGroup cols={2}>
  <Card title="快速开始" icon="rocket" href="/v3/quickstart">
    几分钟内启动并运行DroidRun
  </Card>
    <Card title="指南" icon="code" href="/v3/guides">
   查看示例用例的模板
  </Card>
  <Card title="代理设置" icon="robot" href="/v3/concepts/agent">
   查看您可以为代理进行的配置
  </Card>
    <Card title="支持的LLM" icon="brain" href="/v3/concepts/models">
   DroidRun支持的模型
  </Card>
</CardGroup>

## 核心概念

DroidRun使您能够通过智能代理控制自动化复杂的移动工作流程。无论您是处理简单的数据提取任务还是编排复杂的多应用交互，DroidRun都能提供您所需的灵活性和强大功能。

**您可以通过两种方式使用DroidRun：**

<CardGroup cols={2}>
<Card icon="mobile" title="物理设备" href="/v3/quickstart" arrow>
 - 连接您自己的物理Android设备进行直接自动化
</Card>
<Card icon="cloud" title="云环境" href="https://droidrun.ai/cloud">
 - 访问我们的托管云环境，无需任何设置即可进行即时移动应用自动化。
</Card>
</CardGroup>
