"""
设备管理器 - 管理Android设备连接。
"""

from typing import Dict, List, Optional
from droidrun.adb.wrapper import ADBWrapper
from droidrun.adb.device import Device

class DeviceManager:
    """管理Android设备连接。"""

    def __init__(self, adb_path: Optional[str] = None):
        """初始化设备管理器。

        Args:
            adb_path: ADB二进制文件路径
        """
        self._adb = ADBWrapper(adb_path)
        self._devices: Dict[str, Device] = {}

    async def list_devices(self) -> List[Device]:
        """列出已连接的设备。

        Returns:
            已连接设备的列表
        """
        devices_info = await self._adb.get_devices()

        # 更新设备缓存
        current_serials = set()
        for device_info in devices_info:
            serial = device_info["serial"]
            current_serials.add(serial)

            if serial not in self._devices:
                self._devices[serial] = Device(serial, self._adb)

        # 移除已断开连接的设备
        for serial in list(self._devices.keys()):
            if serial not in current_serials:
                del self._devices[serial]
                
        return list(self._devices.values())

    async def get_device(self, serial: str | None = None) -> Optional[Device]:
        """获取特定设备。

        Args:
            serial: 设备序列号

        Returns:
            如果找到则返回设备实例，否则返回None
        """
        if serial and serial in self._devices:
            return self._devices[serial]

        # 尝试查找设备
        devices = await self.list_devices()
        for device in devices:
            if device.serial == serial or not serial:
                return device
                
        return None

    async def connect(self, host: str, port: int = 5555) -> Optional[Device]:
        """通过TCP/IP连接到设备。

        Args:
            host: 设备IP地址
            port: 设备端口

        Returns:
            已连接的设备实例
        """
        try:
            serial = await self._adb.connect(host, port)
            return await self.get_device(serial)
        except Exception:
            return None

    async def disconnect(self, serial: str) -> bool:
        """断开与设备的连接。

        Args:
            serial: 设备序列号

        Returns:
            如果成功断开连接则返回True
        """
        success = await self._adb.disconnect(serial)
        if success and serial in self._devices:
            del self._devices[serial]
        return success 