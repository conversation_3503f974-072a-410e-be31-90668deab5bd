"""
DroidRun语言配置模块

此模块提供了切换AI代理语言设置的功能。
支持中文和英文两种语言模式。
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum

class Language(Enum):
    """支持的语言枚举"""
    CHINESE = "zh"
    ENGLISH = "en"

@dataclass
class LanguageConfig:
    """语言配置类"""
    language: Language = Language.CHINESE
    
    def is_chinese(self) -> bool:
        """检查是否为中文模式"""
        return self.language == Language.CHINESE
    
    def is_english(self) -> bool:
        """检查是否为英文模式"""
        return self.language == Language.ENGLISH

# 全局语言配置实例
_global_language_config = LanguageConfig()

def set_language(language: Language) -> None:
    """
    设置全局语言配置
    
    Args:
        language: 要设置的语言
    """
    global _global_language_config
    _global_language_config.language = language

def get_language() -> Language:
    """
    获取当前语言配置
    
    Returns:
        当前设置的语言
    """
    return _global_language_config.language

def is_chinese() -> bool:
    """
    检查当前是否为中文模式
    
    Returns:
        如果是中文模式返回True，否则返回False
    """
    return _global_language_config.is_chinese()

def is_english() -> bool:
    """
    检查当前是否为英文模式
    
    Returns:
        如果是英文模式返回True，否则返回False
    """
    return _global_language_config.is_english()

# 语言相关的提示词映射
LANGUAGE_PROMPTS = {
    Language.CHINESE: {
        "current_request": "当前请求：",
        "precondition_check": "前置条件是否满足？你的推理过程是什么，下一步要采取什么行动来处理这个请求？",
        "explain_process": "请解释你的思考过程，然后在需要时提供 ```python ... ``` 标签中的代码。",
        "goal": "目标：",
        "task_failed": "任务执行失败。",
        "planning_update": "规划更新：",
        "original_goal": "原始目标：",
        "failed_task": "失败任务描述：",
        "reported_reason": "报告原因：",
        "instruction": "指令：",
        "complete_success": "任务成功完成",
        "complete_failed": "任务执行失败"
    },
    Language.ENGLISH: {
        "current_request": "Current Request:",
        "precondition_check": "Is the precondition met? What is your reasoning and the next step to address this request?",
        "explain_process": "Explain your thought process then provide code in ```python ... ``` tags if needed.",
        "goal": "Goal:",
        "task_failed": "The execution of a task failed.",
        "planning_update": "PLANNING UPDATE:",
        "original_goal": "Original Goal:",
        "failed_task": "Failed Task Description:",
        "reported_reason": "Reported Reason:",
        "instruction": "Instruction:",
        "complete_success": "Task completed successfully",
        "complete_failed": "Task execution failed"
    }
}

def get_prompt_text(key: str) -> str:
    """
    根据当前语言配置获取提示词文本
    
    Args:
        key: 提示词键名
        
    Returns:
        对应语言的提示词文本
    """
    current_lang = get_language()
    return LANGUAGE_PROMPTS.get(current_lang, LANGUAGE_PROMPTS[Language.ENGLISH]).get(key, key)

def format_user_prompt(goal: str) -> str:
    """
    格式化用户提示词
    
    Args:
        goal: 用户目标
        
    Returns:
        格式化后的用户提示词
    """
    if is_chinese():
        return f"""**{get_prompt_text('current_request')}**
{goal}

**{get_prompt_text('precondition_check')}** {get_prompt_text('explain_process')}"""
    else:
        return f"""**{get_prompt_text('current_request')}**
{goal}

**{get_prompt_text('precondition_check')}** {get_prompt_text('explain_process')}"""

def format_planner_user_prompt(goal: str) -> str:
    """
    格式化规划器用户提示词
    
    Args:
        goal: 用户目标
        
    Returns:
        格式化后的规划器用户提示词
    """
    return f"{get_prompt_text('goal')} {goal}"

def format_task_failed_prompt(task_description: str, reason: str, goal: str) -> str:
    """
    格式化任务失败提示词
    
    Args:
        task_description: 任务描述
        reason: 失败原因
        goal: 原始目标
        
    Returns:
        格式化后的任务失败提示词
    """
    if is_chinese():
        return f"""
{get_prompt_text('planning_update')} {get_prompt_text('task_failed')}

{get_prompt_text('failed_task')} "{task_description}"
{get_prompt_text('reported_reason')} {reason}

之前的计划已停止。我已附上代表失败后设备**当前状态**的截图。请分析这些视觉信息。

{get_prompt_text('original_goal')} {goal}

{get_prompt_text('instruction')} 仅基于提供的显示当前状态的截图和之前失败的原因（'{reason}'），生成一个从这个观察到的状态开始的新计划来实现原始目标：'{goal}'。
"""
    else:
        return f"""
{get_prompt_text('planning_update')} {get_prompt_text('task_failed')}

{get_prompt_text('failed_task')} "{task_description}"
{get_prompt_text('reported_reason')} {reason}

The previous plan has been stopped. I have attached a screenshot representing the device's **current state** immediately after the failure. Please analyze this visual information.

{get_prompt_text('original_goal')} {goal}

{get_prompt_text('instruction')} Based **only** on the provided screenshot showing the current state and the reason for the previous failure ('{reason}'), generate a NEW plan starting from this observed state to achieve the original goal: '{goal}'.
"""
