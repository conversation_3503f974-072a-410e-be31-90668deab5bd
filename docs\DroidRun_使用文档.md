# DroidRun 框架完整使用文档

<picture>
  <source media="(prefers-color-scheme: dark)" srcset="./static/droidrun-dark.png">
  <source media="(prefers-color-scheme: light)" srcset="./static/droidrun.png">
  <img src="./static/droidrun.png" width="full">
</picture>

[![GitHub stars](https://img.shields.io/github/stars/droidrun/droidrun?style=social)](https://github.com/droidrun/droidrun/stargazers)
[![Discord](https://img.shields.io/discord/1360219330318696488?color=7289DA&label=Discord&logo=discord&logoColor=white)](https://discord.gg/ZZbKEZZkwK)
[![文档](https://img.shields.io/badge/Documentation-📕-blue)](https://docs.droidrun.ai)
[![基准测试](https://img.shields.io/badge/Benchmark-🏅-teal)](https://droidrun.ai/benchmark)

## 📖 概述

DroidRun 是一个强大的框架，用于通过 LLM（大语言模型）智能体控制 Android 和 iOS 设备。它允许您使用自然语言命令自动化设备交互，为移动设备自动化测试、UI 探索和任务自动化提供了革命性的解决方案。

### 🌟 核心特性

- 🤖 **自然语言控制**：使用自然语言命令控制 Android 和 iOS 设备
- 🔀 **多 LLM 支持**：支持 OpenAI、Anthropic、Gemini、Ollama、DeepSeek 等多种 LLM 提供商
- 🧠 **智能规划**：具备复杂多步骤任务的规划和推理能力
- 💻 **易用 CLI**：提供简单易用的命令行界面，具有增强的调试功能
- 🐍 **Python API**：可扩展的 Python API，用于自定义自动化脚本
- 📸 **视觉理解**：屏幕截图分析，用于设备的视觉理解
- 🫆 **执行跟踪**：使用 Arize Phoenix 进行详细的执行跟踪和调试
- 🔍 **反射机制**：支持任务执行后的反思和优化

### 💡 应用场景

- **移动应用 UI 自动化测试**：自动化测试移动应用的用户界面
- **引导式工作流程**：为非技术用户创建简单的操作指南
- **重复任务自动化**：自动化移动设备上的重复性操作
- **远程设备协助**：为技术水平较低的用户提供远程设备操作支持
- **UI 探索和分析**：使用自然语言命令探索和分析移动应用界面
- **跨平台测试**：同时支持 Android 和 iOS 设备的自动化测试

## 📦 安装指南

### 系统要求

- **Python 版本**：Python 3.10 或更高版本
- **操作系统**：Windows、macOS、Linux
- **设备要求**：
  - Android 设备：需要启用开发者选项和 USB 调试
  - iOS 设备：需要安装 WebDriverAgent（实验性支持）

### 基础安装

#### 1. 安装 DroidRun

```bash
pip install droidrun
```

#### 2. 安装 LLM 提供商依赖

根据您要使用的 LLM 提供商，安装相应的依赖包：

```bash
# OpenAI
pip install llama-index-llms-openai

# Google Gemini
pip install llama-index-llms-google-genai

# Anthropic Claude
pip install llama-index-llms-anthropic

# Ollama (本地模型)
pip install llama-index-llms-ollama

# DeepSeek
pip install llama-index-llms-deepseek

# 或者一次性安装所有支持的提供商
pip install llama-index-llms-openai llama-index-llms-google-genai llama-index-llms-anthropic llama-index-llms-ollama llama-index-llms-deepseek
```

#### 3. 设备连接设置

**Android 设备设置：**

1. 在 Android 设备上启用开发者选项：
   - 进入 `设置` > `关于手机`
   - 连续点击 `版本号` 7 次
   - 返回设置主页，找到 `开发者选项`

2. 启用 USB 调试：
   - 在开发者选项中启用 `USB 调试`
   - 连接设备到电脑时选择 `允许 USB 调试`

3. 验证设备连接：
   ```bash
   droidrun devices
   ```

**iOS 设备设置（实验性）：**

iOS 支持需要额外的 WebDriverAgent 配置，请参考高级配置部分。

#### 4. 安装 Portal APK

DroidRun 需要在 Android 设备上安装 Portal 应用来提供无障碍服务：

```bash
# 自动下载并安装最新版本的 Portal APK
droidrun setup

# 或者手动指定 APK 文件路径
droidrun setup --path=/path/to/droidrun-portal.apk
```

#### 5. 测试连接

```bash
droidrun ping
```

如果一切设置正确，您应该看到连接成功的消息。

### 验证安装

运行以下命令验证安装是否成功：

```bash
# 检查版本
droidrun --version

# 列出连接的设备
droidrun devices

# 测试基本功能
export OPENAI_API_KEY=your_api_key_here
droidrun "打开设置应用" --provider OpenAI --model gpt-4o
```

## 🚀 快速开始教程

### CLI 基础使用

#### 1. 设置 API 密钥

```bash
# OpenAI
export OPENAI_API_KEY=your_openai_api_key

# Google Gemini
export GOOGLE_API_KEY=your_google_api_key

# Anthropic
export ANTHROPIC_API_KEY=your_anthropic_api_key

# DeepSeek
export DEEPSEEK_API_KEY=your_deepseek_api_key
```

#### 2. 基本命令执行

```bash
# 简单命令
droidrun "打开设置应用"

# 指定 LLM 提供商和模型
droidrun "打开计算器并计算 2+3" --provider OpenAI --model gpt-4o

# 启用视觉功能
droidrun "截取屏幕截图并描述当前界面" --vision

# 启用推理模式（用于复杂任务）
droidrun "打开微信，找到张三的聊天记录，发送'你好'" --reasoning

# 启用调试模式
droidrun "检查电池状态" --debug
```

#### 3. 高级功能

```bash
# 启用执行跟踪
droidrun "安装新应用" --tracing

# 保存执行轨迹
droidrun "完成用户注册流程" --save-trajectory

# 组合多个功能
droidrun "测试登录功能" --vision --reasoning --debug --tracing
```

### Python API 使用

#### 1. 基础脚本示例

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from llama_index.llms.openai import OpenAI

async def main():
    # 初始化 LLM
    llm = OpenAI(model="gpt-4o", api_key="your-api-key")

    # 加载 Android 工具
    tools = AdbTools(serial="your-device-serial")  # 或者 None 使用第一个设备

    # 创建智能体
    agent = DroidAgent(
        goal="打开设置应用并检查 Android 版本",
        llm=llm,
        tools=tools,
        max_steps=10,
        vision=True,
        reasoning=True,
        debug=True
    )

    # 执行任务
    result = await agent.run()
    print(f"任务结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

#### 2. 高级配置示例

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from llama_index.llms.google_genai import GoogleGenAI

async def advanced_automation():
    # 使用 Google Gemini
    llm = GoogleGenAI(
        model="models/gemini-2.5-flash",
        api_key="your-google-api-key"
    )

    # 创建工具实例
    tools = await AdbTools.create()  # 自动选择第一个设备

    # 高级智能体配置
    agent = DroidAgent(
        goal="执行完整的应用测试流程：安装应用、注册账户、完成基本操作、卸载应用",
        llm=llm,
        tools=tools,
        max_steps=50,
        timeout=3600,  # 1小时超时
        vision=True,
        reasoning=True,
        reflection=True,  # 启用反思机制
        enable_tracing=True,  # 启用跟踪
        debug=True,
        save_trajectories=True  # 保存执行轨迹
    )

    try:
        result = await agent.run()
        print(f"✅ 任务完成: {result}")

        # 访问执行过程中的截图
        for i, screenshot in enumerate(tools.screenshots):
            print(f"截图 {i+1}: {screenshot['timestamp']}")

        # 访问智能体记忆
        for memory in tools.memory:
            print(f"记忆: {memory}")

    except Exception as e:
        print(f"❌ 任务失败: {e}")

if __name__ == "__main__":
    asyncio.run(advanced_automation())
```

## 🔧 LLM 提供商配置

DroidRun 支持多种 LLM 提供商，每个提供商都有其特定的配置方法和特性。

### OpenAI / OpenAI-Compatible

**支持的模型：** gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-3.5-turbo 等

**配置方法：**

```bash
# 设置 API 密钥
export OPENAI_API_KEY=your_openai_api_key

# CLI 使用
droidrun "打开计算器" --provider OpenAI --model gpt-4o

# 自定义 API 基础 URL（用于兼容的服务）
droidrun "打开设置" --provider OpenAI --model gpt-4o --base-url https://your-custom-endpoint.com
```

**Python API：**

```python
from llama_index.llms.openai import OpenAI

llm = OpenAI(
    model="gpt-4o",
    api_key="your-api-key",
    base_url="https://api.openai.com/v1",  # 可选，自定义端点
    temperature=0.1
)
```

### Google Gemini

**支持的模型：** gemini-2.5-flash, gemini-2.5-pro, gemini-1.5-pro 等

**配置方法：**

```bash
# 设置 API 密钥
export GOOGLE_API_KEY=your_google_api_key

# CLI 使用
droidrun "打开相机应用" --provider GoogleGenAI --model models/gemini-2.5-flash
```

**Python API：**

```python
from llama_index.llms.google_genai import GoogleGenAI

llm = GoogleGenAI(
    model="models/gemini-2.5-flash",
    api_key="your-google-api-key",
    temperature=0.1
)
```

### Anthropic Claude

**支持的模型：** claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022, claude-3-opus-20240229 等

**配置方法：**

```bash
# 设置 API 密钥
export ANTHROPIC_API_KEY=your_anthropic_api_key

# CLI 使用
droidrun "分析当前屏幕内容" --provider Anthropic --model claude-3-5-sonnet-20241022
```

**Python API：**

```python
from llama_index.llms.anthropic import Anthropic

llm = Anthropic(
    model="claude-3-5-sonnet-20241022",
    api_key="your-anthropic-api-key",
    temperature=0.1
)
```

### Ollama（本地模型）

**支持的模型：** qwen2.5vl:3b, llama3.2-vision:11b, llava:latest 等

**前提条件：** 需要先安装并运行 Ollama

```bash
# 安装 Ollama（参考 https://ollama.ai）
# 拉取支持视觉的模型
ollama pull qwen2.5vl:3b

# CLI 使用
droidrun "描述屏幕上的内容" --provider Ollama --model qwen2.5vl:3b
```

**Python API：**

```python
from llama_index.llms.ollama import Ollama

llm = Ollama(
    model="qwen2.5vl:3b",
    base_url="http://localhost:11434",  # Ollama 默认地址
    temperature=0.1
)
```

### DeepSeek

**支持的模型：** deepseek-chat, deepseek-coder 等

**注意：** DeepSeek 模型不支持视觉功能，请勿使用 `--vision` 标志。

**配置方法：**

```bash
# 设置 API 密钥
export DEEPSEEK_API_KEY=your_deepseek_api_key

# CLI 使用
droidrun "打开设置应用" --provider DeepSeek --model deepseek-chat
```

**Python API：**

```python
from llama_index.llms.deepseek import DeepSeek

llm = DeepSeek(
    model="deepseek-chat",
    api_key="your-deepseek-api-key",
    temperature=0.1
)
```

### 提供商选择建议

| 提供商 | 优势 | 适用场景 | 视觉支持 |
|--------|------|----------|----------|
| **OpenAI** | 稳定性高，响应快速 | 生产环境，复杂推理任务 | ✅ |
| **Google Gemini** | 多模态能力强，成本较低 | 视觉任务，大规模部署 | ✅ |
| **Anthropic** | 安全性高，推理能力强 | 敏感环境，复杂分析 | ✅ |
| **Ollama** | 本地部署，数据隐私 | 离线环境，数据敏感场景 | ✅ |
| **DeepSeek** | 成本低，中文支持好 | 文本任务，预算有限 | ❌ |

## 📱 核心功能详解

### 设备控制能力

DroidRun 提供了全面的 Android 设备控制功能：

#### UI 交互

- **点击操作**：精确点击屏幕元素
- **滑动手势**：支持各种滑动操作
- **文本输入**：在输入框中输入文本
- **长按操作**：长按触发上下文菜单
- **多点触控**：支持缩放等多点手势

#### 应用管理

- **应用启动**：通过包名或应用名启动应用
- **应用安装/卸载**：管理设备上的应用
- **应用切换**：在不同应用间切换
- **后台应用管理**：查看和管理后台运行的应用

#### 系统操作

- **系统设置访问**：访问和修改系统设置
- **通知管理**：读取和操作通知
- **文件系统操作**：访问设备文件系统
- **硬件控制**：控制音量、亮度等硬件功能

### 视觉和截图分析

#### 屏幕截图功能

```python
# 获取当前屏幕截图
screenshot = await tools.take_screenshot()

# 分析截图内容
analysis = await tools.analyze_screen("描述当前屏幕上的主要元素")
```

#### 视觉理解能力

- **元素识别**：识别屏幕上的按钮、文本、图像等元素
- **布局分析**：理解界面布局和层次结构
- **状态检测**：检测应用状态和界面变化
- **文本提取**：从截图中提取文本内容

### 规划和推理功能

#### 任务规划

DroidRun 的规划系统可以将复杂任务分解为多个步骤：

```python
agent = DroidAgent(
    goal="完成用户注册流程：填写个人信息、验证邮箱、设置密码",
    llm=llm,
    tools=tools,
    reasoning=True,  # 启用规划功能
    max_steps=30
)
```

#### 推理能力

- **上下文理解**：理解当前界面状态和用户意图
- **步骤规划**：将复杂任务分解为可执行的步骤
- **错误恢复**：当操作失败时自动尝试替代方案
- **动态调整**：根据执行结果调整后续步骤

### 执行跟踪与调试

#### Arize Phoenix 集成

```python
# 启用 Phoenix 跟踪
agent = DroidAgent(
    goal="your_task",
    llm=llm,
    tools=tools,
    enable_tracing=True
)

# 运行后可在 Phoenix UI 中查看详细跟踪信息
# 访问 http://localhost:6006
```

#### 调试功能

- **详细日志**：记录每个操作的详细信息
- **截图历史**：保存执行过程中的所有截图
- **错误追踪**：详细的错误信息和堆栈跟踪
- **性能监控**：监控执行时间和资源使用

## 📚 API 参考

### DroidAgent 类

`DroidAgent` 是 DroidRun 的核心类，负责协调任务规划和执行。

#### 构造函数

```python
DroidAgent(
    goal: str,                          # 要执行的任务目标
    llm: LLM,                          # 语言模型实例
    tools: Tools,                      # 设备工具实例
    personas: List[AgentPersona] = [DEFAULT],  # 智能体人格设置
    max_steps: int = 15,               # 最大执行步数
    timeout: int = 1000,               # 超时时间（秒）
    vision: bool = False,              # 是否启用视觉功能
    reasoning: bool = False,           # 是否启用推理规划
    reflection: bool = False,          # 是否启用反思机制
    enable_tracing: bool = False,      # 是否启用执行跟踪
    debug: bool = False,               # 是否启用调试模式
    save_trajectories: bool = False    # 是否保存执行轨迹
)
```

#### 主要方法

```python
# 执行任务
async def run() -> str:
    """执行智能体任务并返回结果"""
    pass

# 获取执行状态
def get_status() -> dict:
    """获取当前执行状态"""
    pass
```

#### 使用示例

```python
from droidrun import DroidAgent, AdbTools
from llama_index.llms.openai import OpenAI

# 创建智能体
agent = DroidAgent(
    goal="打开微信，查看最新消息",
    llm=OpenAI(model="gpt-4o", api_key="your-key"),
    tools=AdbTools(serial="device-serial"),
    vision=True,
    reasoning=True,
    debug=True
)

# 执行任务
result = await agent.run()
```

### AdbTools 类

`AdbTools` 提供了与 Android 设备交互的所有工具方法。

#### 构造函数

```python
AdbTools(serial: str)  # 指定设备序列号

# 或者使用类方法自动选择设备
tools = await AdbTools.create(serial=None)
```

#### 核心方法

```python
# 屏幕截图
async def take_screenshot() -> str:
    """获取设备屏幕截图"""
    pass

# 点击操作
async def tap(x: int, y: int) -> str:
    """在指定坐标点击"""
    pass

async def tap_element(element_description: str) -> str:
    """点击描述的元素"""
    pass

# 文本输入
async def input_text(text: str) -> str:
    """输入文本"""
    pass

# 滑动操作
async def swipe(start_x: int, start_y: int, end_x: int, end_y: int) -> str:
    """执行滑动操作"""
    pass

# 应用管理
async def open_app(package_name: str) -> str:
    """打开指定应用"""
    pass

async def install_app(apk_path: str) -> str:
    """安装应用"""
    pass

async def uninstall_app(package_name: str) -> str:
    """卸载应用"""
    pass

# 系统操作
async def press_key(key: str) -> str:
    """按下系统按键（如 home, back, menu）"""
    pass

async def get_device_info() -> dict:
    """获取设备信息"""
    pass
```

#### 高级功能

```python
# 等待元素出现
async def wait_for_element(element_description: str, timeout: int = 10) -> bool:
    """等待指定元素出现"""
    pass

# 滚动查找元素
async def scroll_to_find(element_description: str, direction: str = "down") -> bool:
    """滚动查找元素"""
    pass

# 获取屏幕文本
async def get_screen_text() -> str:
    """获取当前屏幕上的所有文本"""
    pass

# 检查元素是否存在
async def element_exists(element_description: str) -> bool:
    """检查元素是否存在"""
    pass
```

### IOSTools 类

`IOSTools` 提供了与 iOS 设备交互的工具方法（实验性功能）。

#### 构造函数

```python
IOSTools(url: str)  # WebDriverAgent URL
```

#### 基本方法

```python
# 与 AdbTools 类似的接口
async def take_screenshot() -> str
async def tap(x: int, y: int) -> str
async def input_text(text: str) -> str
async def swipe(start_x: int, start_y: int, end_x: int, end_y: int) -> str
```

### DeviceManager 类

`DeviceManager` 负责设备连接和管理。

#### 主要方法

```python
# 列出连接的设备
async def list_devices() -> List[Device]:
    """获取所有连接的设备列表"""
    pass

# 获取特定设备
async def get_device(serial: str) -> Optional[Device]:
    """根据序列号获取设备实例"""
    pass

# TCP/IP 连接
async def connect(host: str, port: int = 5555) -> Optional[Device]:
    """通过 TCP/IP 连接设备"""
    pass
```

#### 使用示例

```python
from droidrun import DeviceManager

manager = DeviceManager()

# 列出所有设备
devices = await manager.list_devices()
for device in devices:
    print(f"设备: {device.serial}, 状态: {device.state}")

# 连接网络设备
device = await manager.connect("*************", 5555)
```

### 工具函数

#### load_llm 函数

```python
from droidrun import load_llm

# 动态加载 LLM
llm = load_llm(
    provider_name="OpenAI",
    model="gpt-4o",
    api_key="your-key",
    base_url="https://api.openai.com/v1",  # 可选
    temperature=0.1
)
```

#### describe_tools 函数

```python
from droidrun.tools import describe_tools

# 获取工具描述
tools_description = describe_tools(AdbTools)
print(tools_description)
```

## 🖥️ CLI 参考

### 基本命令结构

```bash
droidrun [GLOBAL_OPTIONS] COMMAND [COMMAND_OPTIONS] [ARGUMENTS]
```

### 全局选项

```bash
--device, -d TEXT           # 设备序列号或 IP 地址
--provider, -p TEXT         # LLM 提供商 (默认: GoogleGenAI)
--model, -m TEXT           # LLM 模型名称 (默认: models/gemini-2.5-flash)
--steps INTEGER            # 最大执行步数 (默认: 15)
--base-url TEXT           # 自定义 API 基础 URL
--api-base TEXT           # API 基础地址（已弃用，使用 --base-url）
--temperature FLOAT       # 模型温度参数 (默认: 0)
--vision                  # 启用视觉功能
--reasoning               # 启用推理规划模式
--reflection              # 启用反思机制
--tracing                 # 启用 Arize Phoenix 跟踪
--debug                   # 启用详细调试日志
--save-trajectory         # 保存执行轨迹到文件
```

### 主要命令

#### run - 执行任务

```bash
droidrun run "任务描述" [OPTIONS]

# 示例
droidrun run "打开设置应用" --provider OpenAI --model gpt-4o --vision
droidrun run "发送微信消息给张三" --reasoning --debug
```

#### devices - 列出设备

```bash
droidrun devices

# 输出示例：
# 连接的设备:
# • emulator-5554 (device)
# • *************:5555 (device)
```

#### connect - 连接网络设备

```bash
droidrun connect <IP地址> [--port PORT]

# 示例
droidrun connect ************* --port 5555
```

#### setup - 安装 Portal APK

```bash
droidrun setup [--path APK_PATH] [--device DEVICE] [--debug]

# 示例
droidrun setup                                    # 自动下载并安装
droidrun setup --path ./droidrun-portal.apk     # 使用本地 APK
droidrun setup --device emulator-5554           # 指定设备
```

#### ping - 测试连接

```bash
droidrun ping [--device DEVICE] [--debug]

# 示例
droidrun ping                        # 测试默认设备
droidrun ping --device emulator-5554  # 测试指定设备
```

### 使用示例

#### 基础使用

```bash
# 简单任务
droidrun "打开计算器"

# 指定设备
droidrun "截取屏幕截图" --device emulator-5554

# 使用不同的 LLM
droidrun "检查电池状态" --provider Anthropic --model claude-3-5-sonnet-20241022
```

#### 高级功能

```bash
# 启用所有高级功能
droidrun "完成用户注册流程" \
  --vision \
  --reasoning \
  --reflection \
  --tracing \
  --debug \
  --save-trajectory \
  --steps 50

# 使用本地模型
droidrun "分析当前界面" --provider Ollama --model qwen2.5vl:3b --vision

# 自定义 API 端点
droidrun "打开设置" --provider OpenAI --model gpt-4o --base-url https://your-proxy.com/v1
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 设备连接问题

**问题：** `No devices found` 或设备无法连接

**解决方案：**

```bash
# 检查 ADB 连接
adb devices

# 重启 ADB 服务
adb kill-server
adb start-server

# 检查 USB 调试是否启用
# 在设备上：设置 > 开发者选项 > USB 调试

# 检查设备授权
# 连接设备时选择"始终允许此计算机进行调试"
```

**网络连接问题：**

```bash
# 确保设备和电脑在同一网络
# 在设备上启用网络 ADB
adb tcpip 5555

# 连接网络设备
droidrun connect <设备IP地址>

# 如果连接失败，尝试重新启用 USB 调试
adb usb
adb tcpip 5555
```

#### 2. Portal APK 问题

**问题：** Portal 应用安装失败或无障碍服务未启用

**解决方案：**

```bash
# 重新安装 Portal APK
droidrun setup --debug

# 手动启用无障碍服务
# 设备上：设置 > 无障碍 > DroidRun Portal > 启用

# 检查 Portal 状态
droidrun ping --debug

# 如果仍有问题，尝试手动下载 APK
# 从 https://github.com/droidrun/droidrun-portal/releases 下载
droidrun setup --path /path/to/droidrun-portal.apk
```

#### 3. LLM 提供商问题

**问题：** API 密钥错误或模型不可用

**解决方案：**

```bash
# 检查 API 密钥设置
echo $OPENAI_API_KEY
echo $GOOGLE_API_KEY

# 测试 API 连接
droidrun "简单测试" --provider OpenAI --model gpt-4o --debug

# 使用不同的模型
droidrun "测试" --provider GoogleGenAI --model models/gemini-2.5-flash

# 检查网络连接和代理设置
export https_proxy=http://your-proxy:port
```

**Ollama 本地模型问题：**

```bash
# 确保 Ollama 服务运行
ollama serve

# 检查可用模型
ollama list

# 拉取支持视觉的模型
ollama pull qwen2.5vl:3b

# 测试本地模型
droidrun "测试" --provider Ollama --model qwen2.5vl:3b
```

#### 4. 执行超时问题

**问题：** 任务执行超时或卡住

**解决方案：**

```bash
# 增加超时时间和步数
droidrun "复杂任务" --steps 50 --timeout 3600

# 启用调试模式查看详细信息
droidrun "任务" --debug

# 分解复杂任务为简单步骤
droidrun "第一步：打开应用" --debug
droidrun "第二步：执行操作" --debug
```

#### 5. 视觉功能问题

**问题：** 视觉分析不准确或失败

**解决方案：**

```bash
# 确保使用支持视觉的模型
droidrun "描述屏幕" --provider OpenAI --model gpt-4o --vision

# 检查屏幕截图质量
droidrun "截图测试" --debug --save-trajectory

# 尝试不同的 LLM 提供商
droidrun "视觉任务" --provider GoogleGenAI --model models/gemini-2.5-flash --vision
```

#### 6. 内存和性能问题

**问题：** 执行过程中内存不足或性能下降

**解决方案：**

```bash
# 减少最大步数
droidrun "任务" --steps 10

# 禁用不必要的功能
droidrun "任务" --no-vision --no-tracing

# 清理设备缓存
adb shell pm clear com.droidrun.portal

# 重启设备
adb reboot
```

### 调试技巧

#### 1. 启用详细日志

```bash
# 启用调试模式
droidrun "任务" --debug

# 保存执行轨迹
droidrun "任务" --save-trajectory --debug

# 启用跟踪
droidrun "任务" --tracing --debug
```

#### 2. 分步执行

```python
# Python 中分步调试
import asyncio
from droidrun import AdbTools, DroidAgent

async def debug_execution():
    tools = AdbTools(serial="your-device")

    # 先截图查看当前状态
    screenshot = await tools.take_screenshot()
    print(f"当前截图: {screenshot}")

    # 分步执行
    result1 = await tools.tap_element("设置按钮")
    print(f"步骤1结果: {result1}")

    # 等待界面加载
    await asyncio.sleep(2)

    result2 = await tools.tap_element("关于手机")
    print(f"步骤2结果: {result2}")

asyncio.run(debug_execution())
```

#### 3. 查看执行历史

```python
# 访问执行历史
agent = DroidAgent(goal="任务", llm=llm, tools=tools, save_trajectories=True)
result = await agent.run()

# 查看截图历史
for i, screenshot in enumerate(tools.screenshots):
    print(f"截图 {i}: {screenshot['timestamp']}")

# 查看智能体记忆
for memory in tools.memory:
    print(f"记忆: {memory}")
```

### 错误代码参考

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| `DEVICE_NOT_FOUND` | 设备未找到 | 检查设备连接和 USB 调试 |
| `PORTAL_NOT_INSTALLED` | Portal 应用未安装 | 运行 `droidrun setup` |
| `ACCESSIBILITY_NOT_ENABLED` | 无障碍服务未启用 | 手动启用 Portal 无障碍服务 |
| `API_KEY_INVALID` | API 密钥无效 | 检查并重新设置 API 密钥 |
| `MODEL_NOT_AVAILABLE` | 模型不可用 | 使用其他可用模型 |
| `TIMEOUT_ERROR` | 执行超时 | 增加超时时间或减少任务复杂度 |
| `SCREENSHOT_FAILED` | 截图失败 | 检查设备状态和权限 |
| `ELEMENT_NOT_FOUND` | 元素未找到 | 检查元素描述或等待界面加载 |

### 获取帮助

如果遇到无法解决的问题：

1. **查看官方文档**：https://docs.droidrun.ai
2. **GitHub Issues**：https://github.com/droidrun/droidrun/issues
3. **Discord 社区**：https://discord.gg/ZZbKEZZkwK
4. **提交 Bug 报告**：包含详细的错误信息、设备信息和复现步骤

## 🚀 高级用法

### 复杂自动化场景

#### 1. 应用测试自动化

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from llama_index.llms.openai import OpenAI

async def app_testing_automation():
    """完整的应用测试自动化流程"""

    llm = OpenAI(model="gpt-4o", api_key="your-api-key")
    tools = await AdbTools.create()

    # 测试场景列表
    test_scenarios = [
        "安装测试应用",
        "完成用户注册流程",
        "测试主要功能",
        "测试错误处理",
        "性能压力测试",
        "卸载应用"
    ]

    results = {}

    for scenario in test_scenarios:
        print(f"🧪 执行测试场景: {scenario}")

        agent = DroidAgent(
            goal=f"执行测试场景: {scenario}",
            llm=llm,
            tools=tools,
            max_steps=30,
            vision=True,
            reasoning=True,
            reflection=True,
            debug=True,
            save_trajectories=True
        )

        try:
            result = await agent.run()
            results[scenario] = {"status": "success", "result": result}
            print(f"✅ {scenario} - 成功")
        except Exception as e:
            results[scenario] = {"status": "failed", "error": str(e)}
            print(f"❌ {scenario} - 失败: {e}")

    # 生成测试报告
    print("\n📊 测试报告:")
    for scenario, result in results.items():
        status = "✅" if result["status"] == "success" else "❌"
        print(f"{status} {scenario}: {result.get('result', result.get('error'))}")

if __name__ == "__main__":
    asyncio.run(app_testing_automation())
```

#### 2. 多设备并行测试

```python
#!/usr/bin/env python3
import asyncio
from concurrent.futures import ThreadPoolExecutor
from droidrun import AdbTools, DroidAgent, DeviceManager
from llama_index.llms.openai import OpenAI

async def test_on_device(device_serial: str, test_case: str):
    """在单个设备上执行测试"""
    llm = OpenAI(model="gpt-4o", api_key="your-api-key")
    tools = AdbTools(serial=device_serial)

    agent = DroidAgent(
        goal=test_case,
        llm=llm,
        tools=tools,
        max_steps=20,
        vision=True,
        reasoning=True
    )

    try:
        result = await agent.run()
        return {"device": device_serial, "status": "success", "result": result}
    except Exception as e:
        return {"device": device_serial, "status": "failed", "error": str(e)}

async def multi_device_testing():
    """多设备并行测试"""

    # 获取所有连接的设备
    manager = DeviceManager()
    devices = await manager.list_devices()

    if len(devices) < 2:
        print("需要至少2个设备进行并行测试")
        return

    # 测试用例
    test_cases = [
        "测试应用启动性能",
        "测试用户界面响应",
        "测试网络连接功能",
        "测试数据存储功能"
    ]

    # 创建并行任务
    tasks = []
    for i, device in enumerate(devices):
        test_case = test_cases[i % len(test_cases)]
        task = test_on_device(device.serial, test_case)
        tasks.append(task)

    # 并行执行
    print(f"🚀 在 {len(devices)} 个设备上并行执行测试...")
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 输出结果
    print("\n📊 并行测试结果:")
    for result in results:
        if isinstance(result, dict):
            status = "✅" if result["status"] == "success" else "❌"
            print(f"{status} 设备 {result['device']}: {result.get('result', result.get('error'))}")
        else:
            print(f"❌ 执行异常: {result}")

if __name__ == "__main__":
    asyncio.run(multi_device_testing())
```

#### 3. 智能监控和报警

```python
#!/usr/bin/env python3
import asyncio
import time
from datetime import datetime
from droidrun import AdbTools, DroidAgent
from llama_index.llms.openai import OpenAI

class AppMonitor:
    """应用监控类"""

    def __init__(self, device_serial: str, app_package: str):
        self.device_serial = device_serial
        self.app_package = app_package
        self.llm = OpenAI(model="gpt-4o", api_key="your-api-key")
        self.tools = AdbTools(serial=device_serial)
        self.monitoring = False

    async def check_app_health(self):
        """检查应用健康状态"""
        agent = DroidAgent(
            goal=f"检查应用 {self.app_package} 的运行状态，包括是否崩溃、响应是否正常、界面是否正确显示",
            llm=self.llm,
            tools=self.tools,
            max_steps=10,
            vision=True,
            reasoning=True
        )

        try:
            result = await agent.run()
            return {"status": "healthy", "details": result}
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    async def send_alert(self, message: str):
        """发送报警通知"""
        print(f"🚨 报警 [{datetime.now()}]: {message}")

        # 这里可以集成实际的报警系统
        # 例如：发送邮件、Slack 通知、钉钉消息等

    async def start_monitoring(self, interval: int = 300):
        """开始监控（间隔时间：秒）"""
        self.monitoring = True
        print(f"🔍 开始监控应用 {self.app_package}，检查间隔: {interval}秒")

        consecutive_failures = 0
        max_failures = 3

        while self.monitoring:
            try:
                health_status = await self.check_app_health()

                if health_status["status"] == "healthy":
                    consecutive_failures = 0
                    print(f"✅ [{datetime.now()}] 应用状态正常")
                else:
                    consecutive_failures += 1
                    print(f"⚠️ [{datetime.now()}] 应用状态异常: {health_status.get('error')}")

                    if consecutive_failures >= max_failures:
                        await self.send_alert(f"应用 {self.app_package} 连续 {consecutive_failures} 次检查失败")
                        consecutive_failures = 0  # 重置计数器

                await asyncio.sleep(interval)

            except Exception as e:
                print(f"❌ 监控过程中出现错误: {e}")
                await asyncio.sleep(interval)

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        print("🛑 监控已停止")

async def main():
    """主函数"""
    monitor = AppMonitor(
        device_serial="your-device-serial",
        app_package="com.example.app"
    )

    # 开始监控
    await monitor.start_monitoring(interval=60)  # 每分钟检查一次

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n监控已手动停止")
```

### 最佳实践

#### 1. 错误处理和重试机制

```python
import asyncio
from typing import Optional
from droidrun import AdbTools, DroidAgent

class RobustAgent:
    """具有错误处理和重试机制的智能体"""

    def __init__(self, llm, tools, max_retries: int = 3):
        self.llm = llm
        self.tools = tools
        self.max_retries = max_retries

    async def execute_with_retry(self, goal: str, **kwargs) -> Optional[str]:
        """带重试机制的任务执行"""

        for attempt in range(self.max_retries):
            try:
                print(f"🔄 尝试执行任务 (第 {attempt + 1}/{self.max_retries} 次): {goal}")

                agent = DroidAgent(
                    goal=goal,
                    llm=self.llm,
                    tools=self.tools,
                    **kwargs
                )

                result = await agent.run()
                print(f"✅ 任务执行成功")
                return result

            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次尝试失败: {e}")

                if attempt < self.max_retries - 1:
                    # 等待后重试
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)

                    # 尝试恢复设备状态
                    await self.recover_device_state()
                else:
                    print(f"💥 任务最终失败，已达到最大重试次数")
                    raise e

        return None

    async def recover_device_state(self):
        """尝试恢复设备状态"""
        try:
            # 返回主屏幕
            await self.tools.press_key("home")
            await asyncio.sleep(1)

            # 清除最近任务
            await self.tools.press_key("recent")
            await asyncio.sleep(1)
            await self.tools.press_key("back")

            print("🔧 设备状态已恢复")
        except Exception as e:
            print(f"⚠️ 设备状态恢复失败: {e}")
```

#### 2. 性能优化

```python
import asyncio
from contextlib import asynccontextmanager
from droidrun import AdbTools

class OptimizedTools:
    """优化的工具类"""

    def __init__(self, serial: str):
        self.tools = AdbTools(serial=serial)
        self._screenshot_cache = {}
        self._cache_timeout = 5  # 缓存5秒

    @asynccontextmanager
    async def batch_operations(self):
        """批量操作上下文管理器"""
        print("🚀 开始批量操作")
        try:
            yield self
        finally:
            print("✅ 批量操作完成")
            # 清理缓存
            self._screenshot_cache.clear()

    async def cached_screenshot(self) -> str:
        """带缓存的截图功能"""
        current_time = asyncio.get_event_loop().time()

        # 检查缓存
        if 'screenshot' in self._screenshot_cache:
            cache_time, screenshot = self._screenshot_cache['screenshot']
            if current_time - cache_time < self._cache_timeout:
                return screenshot

        # 获取新截图
        screenshot = await self.tools.take_screenshot()
        self._screenshot_cache['screenshot'] = (current_time, screenshot)

        return screenshot

    async def smart_wait(self, element_description: str, timeout: int = 10) -> bool:
        """智能等待元素出现"""
        start_time = asyncio.get_event_loop().time()

        while asyncio.get_event_loop().time() - start_time < timeout:
            if await self.tools.element_exists(element_description):
                return True

            await asyncio.sleep(0.5)  # 短暂等待

        return False

# 使用示例
async def optimized_workflow():
    tools = OptimizedTools(serial="device-serial")

    async with tools.batch_operations():
        # 批量操作
        screenshot1 = await tools.cached_screenshot()  # 实际截图
        screenshot2 = await tools.cached_screenshot()  # 使用缓存

        # 智能等待
        if await tools.smart_wait("登录按钮", timeout=10):
            await tools.tools.tap_element("登录按钮")
```

#### 3. 配置管理

```python
import json
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class DroidRunConfig:
    """DroidRun 配置类"""

    # LLM 配置
    llm_provider: str = "OpenAI"
    llm_model: str = "gpt-4o"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = 0.1

    # 智能体配置
    max_steps: int = 15
    timeout: int = 1000
    vision: bool = True
    reasoning: bool = True
    reflection: bool = False
    debug: bool = False

    # 设备配置
    device_serial: Optional[str] = None

    # 高级配置
    enable_tracing: bool = False
    save_trajectories: bool = False
    retry_attempts: int = 3

    @classmethod
    def from_file(cls, config_path: str) -> 'DroidRunConfig':
        """从配置文件加载"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        else:
            # 创建默认配置文件
            config = cls()
            config.save_to_file(config_path)
            return config

    def save_to_file(self, config_path: str):
        """保存到配置文件"""
        config_data = {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }

        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)

    def get_api_key(self) -> str:
        """获取 API 密钥"""
        if self.api_key:
            return self.api_key

        # 从环境变量获取
        env_key_map = {
            "OpenAI": "OPENAI_API_KEY",
            "GoogleGenAI": "GOOGLE_API_KEY",
            "Anthropic": "ANTHROPIC_API_KEY",
            "DeepSeek": "DEEPSEEK_API_KEY"
        }

        env_key = env_key_map.get(self.llm_provider)
        if env_key:
            return os.getenv(env_key, "")

        return ""

# 使用示例
config = DroidRunConfig.from_file("~/.droidrun/config.json")
print(f"当前配置: {config}")
```

这样，我们就完成了 DroidRun 框架的全面使用文档。文档涵盖了从基础安装到高级用法的所有内容，包括详细的 API 参考、CLI 命令说明、故障排除指南和最佳实践。文档以中文编写，适合中文用户使用，同时保持了专业性和实用性。