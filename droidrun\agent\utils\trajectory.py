"""
DroidRun代理的轨迹实用工具。

此模块提供用于处理代理轨迹的辅助函数，
包括保存、加载和分析它们。
"""

import json
import logging
import os
import time
from typing import Dict, List, Any
from PIL import Image
import io
from llama_index.core.workflow import Event

logger = logging.getLogger("droidrun")

class Trajectory:

    def __init__(self):
        """初始化一个空的轨迹类。"""
        self.events: List[Event] = []
        self.screenshots: List[bytes] = []


    def create_screenshot_gif(self, output_path: str, duration: int = 1000) -> str:
        """
        从屏幕截图列表创建GIF。

        Args:
            output_path: GIF的基础路径（不含扩展名）
            duration: 每帧的持续时间（毫秒）

        Returns:
            创建的GIF文件路径
        """
        if len(self.screenshots) == 0:
            return None
            
        images = []
        for screenshot in self.screenshots:
            img_data = screenshot
            img = Image.open(io.BytesIO(img_data))
            images.append(img)
        
        # 保存为GIF
        gif_path = f"{output_path}.gif"
        images[0].save(
            gif_path,
            save_all=True,
            append_images=images[1:],
            duration=duration,
            loop=0
        )
        
        return gif_path

    def save_trajectory(
        self,
        directory: str = "trajectories",
    ) -> str:
        """
        将轨迹步骤保存到JSON文件，如果有屏幕截图则创建GIF。

        Args:
            directory: 保存轨迹文件的目录

        Returns:
            保存的轨迹文件路径
        """
        os.makedirs(directory, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        base_path = os.path.join(directory, f"trajectory_{timestamp}")
        
        def make_serializable(obj):
            """递归地使对象可JSON序列化。"""
            if hasattr(obj, "__class__") and obj.__class__.__name__ == "ChatMessage":
                # 从ChatMessage中提取文本内容
                if hasattr(obj, "content") and obj.content is not None:
                    return {"role": obj.role.value, "content": obj.content}
                # 如果内容不可用，尝试从块中提取
                elif hasattr(obj, "blocks") and obj.blocks:
                    text_content = ""
                    for block in obj.blocks:
                        if hasattr(block, "text"):
                            text_content += block.text
                    return {"role": obj.role.value, "content": text_content}
                else:
                    return str(obj)
            elif isinstance(obj, dict):
                return {k: make_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [make_serializable(item) for item in obj]
            elif hasattr(obj, "__dict__"):
                # 通过转换为字典处理其他自定义对象
                return {k: make_serializable(v) for k, v in obj.__dict__.items()
                       if not k.startswith('_')}
            else:
                return obj
        
        serializable_events = []
        for event in self.events:
            event_dict = {
                "type": event.__class__.__name__,
                **{k: make_serializable(v) for k, v in event.__dict__.items() 
                   if not k.startswith('_')}
            }
            serializable_events.append(event_dict)
        
        json_path = f"{base_path}.json"
        with open(json_path, "w") as f:
            json.dump(serializable_events, f, indent=2)

        self.create_screenshot_gif(base_path)

        return json_path

    def get_trajectory_statistics(trajectory_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取轨迹的统计信息。

        Args:
            trajectory_data: 轨迹数据字典

        Returns:
            包含轨迹统计信息的字典
        """
        trajectory_steps = trajectory_data.get("trajectory_steps", [])

        # 计算不同类型的步骤
        step_types = {}
        for step in trajectory_steps:
            step_type = step.get("type", "unknown")
            step_types[step_type] = step_types.get(step_type, 0) + 1

        # 计算规划与执行步骤
        planning_steps = sum(count for step_type, count in step_types.items()
                            if step_type.startswith("planner_"))
        execution_steps = sum(count for step_type, count in step_types.items()
                            if step_type.startswith("codeact_"))

        # 计算成功与失败的执行
        successful_executions = sum(1 for step in trajectory_steps
                                if step.get("type") == "codeact_execution"
                                and step.get("success", False))
        failed_executions = sum(1 for step in trajectory_steps
                            if step.get("type") == "codeact_execution"
                            and not step.get("success", True))
        
        # 返回统计信息
        return {
            "total_steps": len(trajectory_steps),
            "step_types": step_types,
            "planning_steps": planning_steps,
            "execution_steps": execution_steps,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "goal_achieved": trajectory_data.get("success", False)
        }

    def print_trajectory_summary(self, trajectory_data: Dict[str, Any]) -> None:
        """
        打印轨迹摘要。

        Args:
            trajectory_data: 轨迹数据字典
        """
        stats = self.get_trajectory_statistics(trajectory_data)

        print("=== 轨迹摘要 ===")
        print(f"目标: {trajectory_data.get('goal', '未知')}")
        print(f"成功: {trajectory_data.get('success', False)}")
        print(f"原因: {trajectory_data.get('reason', '未知')}")
        print(f"总步骤数: {stats['total_steps']}")
        print("步骤分解:")
        for step_type, count in stats['step_types'].items():
            print(f"  - {step_type}: {count}")
        print(f"规划步骤: {stats['planning_steps']}")
        print(f"执行步骤: {stats['execution_steps']}")
        print(f"成功执行: {stats['successful_executions']}")
        print(f"失败执行: {stats['failed_executions']}")
        print("==================")