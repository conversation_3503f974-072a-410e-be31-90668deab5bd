"""
Device - Android设备的高级表示。
"""

import os
import tempfile
import time
import random
import string
from typing import Dict, Op<PERSON>, Tuple, List
from droidrun.adb.wrapper import ADBWrapper


class Device:
    """Android设备的高级表示。"""

    def __init__(self, serial: str, adb: ADBWrapper):
        """初始化设备。

        Args:
            serial: 设备序列号
            adb: ADB包装器实例
        """
        self._serial = serial
        self._adb = adb
        self._properties_cache: Dict[str, str] = {}

    @property
    def serial(self) -> str:
        """获取设备序列号。"""
        return self._serial

    async def get_properties(self) -> Dict[str, str]:
        """获取所有设备属性。"""
        if not self._properties_cache:
            self._properties_cache = await self._adb.get_properties(self._serial)
        return self._properties_cache

    async def get_property(self, name: str) -> str:
        """获取特定的设备属性。"""
        props = await self.get_properties()
        return props.get(name, "")

    @property
    async def model(self) -> str:
        """获取设备型号。"""
        return await self.get_property("ro.product.model")

    @property
    async def brand(self) -> str:
        """获取设备品牌。"""
        return await self.get_property("ro.product.brand")

    @property
    async def android_version(self) -> str:
        """获取Android版本。"""
        return await self.get_property("ro.build.version.release")

    @property
    async def sdk_level(self) -> str:
        """获取SDK级别。"""
        return await self.get_property("ro.build.version.sdk")

    async def shell(self, command: str, timeout: float | None = None) -> str:
        """在设备上执行shell命令。"""
        return await self._adb.shell(self._serial, command, timeout)

    async def tap(self, x: int, y: int) -> None:
        """在坐标处点击。

        Args:
            x: X坐标
            y: Y坐标
        """
        await self._adb.shell(self._serial, f"input tap {x} {y}")

    async def swipe(
        self, start_x: int, start_y: int, end_x: int, end_y: int, duration_ms: int = 300
    ) -> None:
        """执行滑动手势。

        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration_ms: 滑动持续时间（毫秒）
        """
        await self._adb.shell(
            self._serial,
            f"input swipe {start_x} {start_y} {end_x} {end_y} {duration_ms}",
        )

    async def input_text(self, text: str) -> None:
        """输入文本。

        Args:
            text: 要输入的文本
        """
        await self._adb.shell(self._serial, f"input text {text}")

    async def press_key(self, keycode: int) -> None:
        """按键。

        Args:
            keycode: 要按下的Android键码
        """
        await self._adb.shell(self._serial, f"input keyevent {keycode}")

    async def start_activity(
        self,
        package: str,
        activity: str = ".MainActivity",
        extras: Optional[Dict[str, str]] = None,
    ) -> None:
        """启动应用活动。

        Args:
            package: 包名
            activity: 活动名称
            extras: Intent额外参数
        """
        cmd = f"am start -n {package}/{activity}"
        if extras:
            for key, value in extras.items():
                cmd += f" -e {key} {value}"
        await self._adb.shell(self._serial, cmd)

    async def start_app(self, package: str, activity: str = "") -> str:
        """在设备上启动应用。

        Args:
            package: 包名
            activity: 可选的活动名称（如果为空，启动默认活动）

        Returns:
            结果消息
        """
        if activity:
            if not activity.startswith(".") and "." not in activity:
                activity = f".{activity}"

            if (
                not activity.startswith(".")
                and "." in activity
                and not activity.startswith(package)
            ):
                # 完全限定的活动名称
                component = activity.split("/", 1)
                return await self.start_activity(
                    component[0], component[1] if len(component) > 1 else activity
                )

            # 相对活动名称
            return await self.start_activity(package, activity)

        # 使用monkey启动主活动
        cmd = f"monkey -p {package} -c android.intent.category.LAUNCHER 1"
        result = await self._adb.shell(self._serial, cmd)
        return f"Started {package}"

    async def install_app(
        self, apk_path: str, reinstall: bool = False, grant_permissions: bool = True
    ) -> str:
        """在设备上安装APK。

        Args:
            apk_path: APK文件的路径
            reinstall: 如果应用存在是否重新安装
            grant_permissions: 是否授予所有请求的权限

        Returns:
            安装结果
        """
        if not os.path.exists(apk_path):
            return f"错误: 找不到APK文件: {apk_path}"

        # 构建安装命令参数
        install_args = ["install"]
        if reinstall:
            install_args.append("-r")
        if grant_permissions:
            install_args.append("-g")
        install_args.append(apk_path)

        try:
            stdout, stderr = await self._adb._run_device_command(
                self._serial,
                install_args,
                timeout=120,  # 安装的更长超时时间
            )

            if "success" in stdout.lower():
                return f"成功安装 {os.path.basename(apk_path)}"
            return f"安装失败: {stdout or stderr}"

        except Exception as e:
            return f"安装失败: {str(e)}"

    async def uninstall_app(self, package: str, keep_data: bool = False) -> str:
        """从设备卸载应用。

        Args:
            package: 要卸载的包名
            keep_data: 是否保留应用数据和缓存目录

        Returns:
            卸载结果
        """
        cmd = ["pm", "uninstall"]
        if keep_data:
            cmd.append("-k")
        cmd.append(package)

        result = await self._adb.shell(self._serial, " ".join(cmd))
        return result.strip()

    async def take_screenshot(self, quality: int = 75) -> Tuple[str, bytes]:
        """截取设备屏幕截图并压缩。

        Args:
            quality: JPEG质量 (1-100，越低文件越小)

        Returns:
            (本地文件路径, 屏幕截图数据字节)的元组
        """
        # 为屏幕截图创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp:
            screenshot_path = temp.name

        try:
            # 为设备生成随机文件名
            timestamp = int(time.time())
            random_suffix = "".join(
                random.choices(string.ascii_lowercase + string.digits, k=8)
            )
            device_path = f"/sdcard/screenshot_{timestamp}_{random_suffix}.png"

            # 使用screencap命令截取屏幕截图
            await self._adb.shell(self._serial, f"screencap -p {device_path}")

            # 将屏幕截图拉取到本地机器
            await self._adb._run_device_command(
                self._serial, ["pull", device_path, screenshot_path]
            )

            # 清理设备上的文件
            await self._adb.shell(self._serial, f"rm {device_path}")

            # 读取屏幕截图文件
            with open(screenshot_path, "rb") as f:
                screenshot_data = f.read()

            # 转换和压缩图像
            try:
                from PIL import Image
                import io

                # 为压缩图像创建缓冲区
                buffer = io.BytesIO()

                # 将PNG数据加载到PIL图像中
                with Image.open(io.BytesIO(screenshot_data)) as img:
                    # 转换为RGB（如果存在则移除alpha通道）并保存为JPEG
                    converted_img = img.convert("RGB") if img.mode == "RGBA" else img
                    converted_img.save(
                        buffer, format="JPEG", quality=quality, optimize=True
                    )
                    compressed_data = buffer.getvalue()

                # 获取大小减少信息用于日志记录
                png_size = len(screenshot_data) / 1024
                jpg_size = len(compressed_data) / 1024
                reduction = 100 - (jpg_size / png_size * 100) if png_size > 0 else 0

                import logging

                logger = logging.getLogger("droidrun")
                logger.debug(
                    f"Screenshot compressed successfully: {png_size:.1f}KB → {jpg_size:.1f}KB ({reduction:.1f}% reduction)"
                )

                return screenshot_path, compressed_data
            except ImportError:
                # 如果PIL不可用，返回原始PNG数据
                logger.warning("PIL不可用，返回未压缩的屏幕截图")
                return screenshot_path, screenshot_data
            except Exception as e:
                # 如果压缩失败，返回原始PNG数据
                logger.warning(
                    f"屏幕截图压缩失败: {e}，返回未压缩的"
                )
                return screenshot_path, screenshot_data

        except Exception as e:
            # 出错时清理
            try:
                os.unlink(screenshot_path)
            except OSError:
                pass
            raise RuntimeError(f"屏幕截图捕获失败: {str(e)}")

    def _parse_package_list(self, output: str) -> List[Dict[str, str]]:
        """解析'pm list packages -f'命令的输出。

        Args:
            output: 来自'pm list packages -f'的原始命令输出

        Returns:
            包含包信息的字典列表，包含'package'和'path'键
        """
        apps = []
        for line in output.splitlines():
            if line.startswith("package:"):
                # 格式是: "package:/path/to/base.apk=com.package.name"
                path_and_pkg = line[8:]  # 去掉"package:"
                if "=" in path_and_pkg:
                    path, package = path_and_pkg.rsplit("=", 1)
                    apps.append({"package": package.strip(), "path": path.strip()})
        return apps

    async def list_packages(self, include_system_apps: bool = False) -> List[str]:
        """
        列出设备上已安装的包。

        Args:
            include_system_apps: 是否包含系统应用（默认: False）

        Returns:
            包名列表
        """
        # 使用直接的ADB命令获取带路径的包
        cmd = ["pm", "list", "packages", "-f"]
        if not include_system_apps:
            cmd.append("-3")

        output = await self.shell(" ".join(cmd))

        # 使用函数解析包列表
        packages = self._parse_package_list(output)
        # 格式化包列表以提高可读性
        package_list = [pack["package"] for pack in packages]
        #for package in package_list:
        #    print(package)
        return package_list
