{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "DroidRun", "colors": {"primary": "#0D9373", "light": "#07C983", "dark": "#0D9373"}, "favicon": "/favicon.png", "navigation": {"versions": [{"version": "0.3.2", "groups": [{"group": "Introduction", "pages": ["v3/overview", "v3/quickstart"]}, {"group": "Guides", "pages": ["v3/guides/overview", "v3/guides/cli", "v3/guides/gemini", "v3/guides/openailike", "v3/guides/ollama", "v3/guides/telemetry"]}, {"group": "Core Concepts", "pages": ["v3/concepts/agent", "v3/concepts/models", "v3/concepts/android-tools", "v3/concepts/portal-app"]}, {"group": "SDK Reference", "pages": ["v3/sdk/droid-agent", "v3/sdk/adb-tools", "v3/sdk/ios-tools", "v3/sdk/base-tools", "v3/sdk/adb-utils"]}]}, {"version": "0.2.0", "groups": [{"group": "Getting Started", "pages": ["v2/overview", "v2/quickstart"]}, {"group": "Core Concepts", "pages": ["v2/concepts/agent", "v2/concepts/planning", "v2/concepts/android-control", "v2/concepts/portal-app", "v2/concepts/tracing"]}]}, {"version": "0.1.0", "groups": [{"group": "Getting Started", "pages": ["v1/overview", "v1/quickstart"]}, {"group": "Core Concepts", "pages": ["v1/concepts/agent", "v1/concepts/android-control", "v1/concepts/portal-app"]}]}]}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg"}, "navbar": {"links": [{"label": "GitHub", "href": "https://github.com/droidrun/droidrun"}, {"label": "Benchmark", "href": "https://droidrun.ai/benchmark"}], "primary": {"type": "button", "label": "Join <PERSON>", "href": "https://discord.gg/gdekvkJFvn"}}, "footer": {"socials": {"github": "https://github.com/droidrun/droidrun", "x": "https://x.com/droid_run", "discord": "https://discord.gg/gdekvkJFvn", "website": "https://droidrun.ai"}}, "errors": {"404": {"redirect": false}}}