"""
DroidAgent - 一个包装类，协调任务的规划和执行，
以在Android设备上实现用户的目标。
"""

import logging
from typing import List

from llama_index.core.llms.llm import LLM
from llama_index.core.workflow import step, StartEvent, StopEvent, Workflow, Context
from llama_index.core.workflow.handler import WorkflowHandler
from droidrun.agent.droid.events import *
from droidrun.agent.codeact import CodeActAgent
from droidrun.agent.codeact.events import EpisodicMemoryEvent
from droidrun.agent.planner import PlannerAgent
from droidrun.agent.context.task_manager import TaskManager
from droidrun.agent.utils.trajectory import Trajectory
from droidrun.tools import Tools, describe_tools
from droidrun.agent.common.events import ScreenshotEvent
from droidrun.agent.common.default import MockWorkflow
from droidrun.agent.context import ContextInjectionManager
from droidrun.agent.context.agent_persona import AgentPersona
from droidrun.agent.context.personas import DEFAULT
from droidrun.agent.oneflows.reflector import Reflector
from droidrun.telemetry import capture, flush, DroidAgentInitEvent, DroidAgentFinalizeEvent


logger = logging.getLogger("droidrun")

class DroidAgent(Workflow):
    """
    一个包装类，协调PlannerAgent（创建规划）和
    CodeActAgent（执行任务）之间的工作，以实现用户的目标。
    """

    @staticmethod
    def _configure_default_logging(debug: bool = False):
        """
        如果没有处理器存在，为DroidAgent配置默认日志记录。
        这确保在直接使用DroidAgent时日志是可见的。
        """
        # 只有在没有处理器存在时才配置（避免重复配置）
        if not logger.handlers:
            # 创建控制台处理器
            handler = logging.StreamHandler()

            # 设置格式
            if debug:
                formatter = logging.Formatter("%(asctime)s %(levelname)s: %(message)s", "%H:%M:%S")
            else:
                formatter = logging.Formatter("%(message)s")
            
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.DEBUG if debug else logging.INFO)
            logger.propagate = False
    
    def __init__(
        self, 
        goal: str,
        llm: LLM,
        tools: Tools,
        personas: List[AgentPersona] = [DEFAULT],
        max_steps: int = 15,
        timeout: int = 1000,
        vision: bool = False,
        reasoning: bool = False,
        reflection: bool = False,
        enable_tracing: bool = False,
        debug: bool = False,
        save_trajectories: bool = False,
        *args,
        **kwargs
    ):
        """
        初始化DroidAgent包装器。

        Args:
            goal: 用户要执行的目标或命令
            llm: 两个代理使用的语言模型
            max_steps: 两个代理的最大步骤数
            timeout: 代理执行超时时间（秒）
            reasoning: 是否使用PlannerAgent进行复杂推理（True）
                      或直接将任务发送给CodeActAgent（False）
            reflection: 是否对CodeActAgent执行的步骤进行反思，为PlannerAgent提供建议
            enable_tracing: 是否启用Arize Phoenix跟踪
            debug: 是否启用详细调试日志
            **kwargs: 传递给代理的额外关键字参数
        """
        super().__init__(timeout=timeout ,*args,**kwargs)
        # 如果尚未配置，则配置默认日志记录
        self._configure_default_logging(debug=debug)

        # 如果启用，首先设置全局跟踪
        if enable_tracing:
            try:
                from llama_index.core import set_global_handler
                set_global_handler("arize_phoenix")
                logger.info("🔍 Arize Phoenix跟踪已全局启用")
            except ImportError:
                logger.warning("⚠️ 未找到Arize Phoenix包，跟踪已禁用")
                enable_tracing = False

        self.goal = goal
        self.llm = llm
        self.vision = vision
        self.max_steps = max_steps
        self.max_codeact_steps = max_steps
        self.timeout = timeout
        self.reasoning = reasoning
        self.reflection = reflection
        self.debug = debug

        self.event_counter = 0
        self.save_trajectories = save_trajectories
        
        self.trajectory = Trajectory()
        self.task_manager = TaskManager()
        self.task_iter = None
        self.cim = ContextInjectionManager(personas=personas)
        self.current_episodic_memory = None

        logger.info("🤖 正在初始化DroidAgent...")

        self.tool_list = describe_tools(tools)
        self.tools_instance = tools


        if self.reasoning:
            logger.info("📝 正在初始化规划代理...")
            self.planner_agent = PlannerAgent(
                goal=goal,
                llm=llm,
                vision=vision,
                personas=personas,
                task_manager=self.task_manager,
                tools_instance=tools,
                timeout=timeout,
                debug=debug
            )
            self.add_workflows(planner_agent=self.planner_agent)
            self.max_codeact_steps = 5

            if self.reflection:
                self.reflector = Reflector(llm=llm, debug=debug)

        else:
            logger.debug("🚫 规划已禁用 - 将直接使用CodeActAgent执行任务")
            self.planner_agent = None

        capture(
            DroidAgentInitEvent(
                goal=goal,
                llm=llm.class_name(),
                tools=",".join(self.tool_list),
                personas=",".join([p.name for p in personas]),
                max_steps=max_steps,
                timeout=timeout,
                vision=vision,
                reasoning=reasoning,
                reflection=reflection,
                enable_tracing=enable_tracing,
                debug=debug,
                save_trajectories=save_trajectories,
            )
        )


        logger.info("✅ DroidAgent初始化成功。")

    def run(self) -> WorkflowHandler:
        """
        运行DroidAgent工作流。
        """
        return super().run()
    
    @step
    async def execute_task(
        self,
        ctx: Context,
        ev: CodeActExecuteEvent
        ) -> CodeActResultEvent:
        """
        使用CodeActAgent执行单个任务。

        Args:
            task: 包含描述和状态的任务字典

        Returns:
            (success, reason)的元组
        """
        task: Task = ev.task
        reflection = ev.reflection if ev.reflection is not None else None
        persona = self.cim.get_persona(task.agent_type)

        logger.info(f"🔧 正在执行任务: {task.description}")

        try:
            codeact_agent = CodeActAgent(
                llm=self.llm,
                persona=persona,
                vision=self.vision,
                max_steps=self.max_codeact_steps,
                all_tools_list=self.tool_list,
                tools_instance=self.tools_instance,
                debug=self.debug,
                timeout=self.timeout,
            )

            handler = codeact_agent.run(
                input=task.description,
                remembered_info=self.tools_instance.memory,
                reflection=reflection
            )
            
            async for nested_ev in handler.stream_events():
                self.handle_stream_event(nested_ev, ctx)

            result = await handler

            
            if "success" in result and result["success"]:
                return CodeActResultEvent(success=True, reason=result["reason"], task=task, steps=result["codeact_steps"])
            else:
                return CodeActResultEvent(success=False, reason=result["reason"], task=task, steps=result["codeact_steps"])
                
        except Exception as e:
            logger.error(f"任务执行期间出错: {e}")
            if self.debug:
                import traceback
                logger.error(traceback.format_exc())
            return CodeActResultEvent(success=False, reason=f"错误: {str(e)}", task=task, steps=[])
    
    @step
    async def handle_codeact_execute(self, ctx: Context, ev: CodeActResultEvent) -> FinalizeEvent | ReflectionEvent:
        try:
            task = ev.task
            if not self.reasoning:
                return FinalizeEvent(success=ev.success, reason=ev.reason, output=ev.reason, task=[task], tasks=[task], steps=ev.steps)
            
            if self.reflection:
                return ReflectionEvent(task=task)
            
            return ReasoningLogicEvent()

        except Exception as e:
            logger.error(f"❌ DroidAgent执行期间出错: {e}")
            if self.debug:
                import traceback
                logger.error(traceback.format_exc())
            tasks = self.task_manager.get_task_history()
            return FinalizeEvent(success=False, reason=str(e), output=str(e), task=tasks, tasks=tasks, steps=self.step_counter)
        

    @step
    async def reflect(
        self,
        ctx: Context,
        ev: ReflectionEvent
        ) -> ReasoningLogicEvent | CodeActExecuteEvent:


        task = ev.task
        if ev.task.agent_type == "AppStarterExpert":
            self.task_manager.complete_task(task)
            return ReasoningLogicEvent()
        
        reflection = await self.reflector.reflect_on_episodic_memory(episodic_memory=self.current_episodic_memory, goal=task.description)

        if reflection.goal_achieved:
            self.task_manager.complete_task(task)
            return ReasoningLogicEvent()
        
        else:
            self.task_manager.fail_task(task)
            return ReasoningLogicEvent(reflection=reflection)
        

    @step
    async def handle_reasoning_logic(
        self,
        ctx: Context,
        ev: ReasoningLogicEvent,
        planner_agent: Workflow = MockWorkflow()
        ) -> FinalizeEvent | CodeActExecuteEvent:
        try:
            if self.step_counter >= self.max_steps:
                output = f"已达到最大步骤数 ({self.max_steps})"
                tasks = self.task_manager.get_task_history()
                return FinalizeEvent(success=False, reason=output, output=output, task=tasks, tasks=tasks, steps=self.step_counter)
            self.step_counter += 1

            if ev.reflection:
                handler = planner_agent.run(remembered_info=self.tools_instance.memory, reflection=ev.reflection)
            else:
                if self.task_iter:
                    try:
                        task = next(self.task_iter)
                        return CodeActExecuteEvent(task=task, reflection=None)
                    except StopIteration as e:
                        logger.info("正在规划下一步...")

                logger.debug(f"规划步骤 {self.step_counter}/{self.max_steps}")

                handler = planner_agent.run(remembered_info=self.tools_instance.memory, reflection=None)

            async for nested_ev in handler.stream_events():
                self.handle_stream_event(nested_ev, ctx)

            result = await handler

            self.tasks = self.task_manager.get_all_tasks()
            self.task_iter = iter(self.tasks)

            if self.task_manager.goal_completed:
                logger.info(f"✅ 目标完成: {self.task_manager.message}")
                tasks = self.task_manager.get_task_history()
                return FinalizeEvent(success=True, reason=self.task_manager.message, output=self.task_manager.message, task=tasks, tasks=tasks, steps=self.step_counter)
            if not self.tasks:
                logger.warning("规划器未生成任务")
                output = "规划器未生成任何任务"
                tasks = self.task_manager.get_task_history()
                return FinalizeEvent(success=False, reason=output, output=output, task=tasks, tasks=tasks, steps=self.step_counter)

            return CodeActExecuteEvent(task=next(self.task_iter), reflection=None)

        except Exception as e:
            logger.error(f"❌ DroidAgent执行期间出错: {e}")
            if self.debug:
                import traceback
                logger.error(traceback.format_exc())
            tasks = self.task_manager.get_task_history()
            return FinalizeEvent(success=False, reason=str(e), output=str(e), task=tasks, tasks=tasks, steps=self.step_counter)
    

    @step
    async def start_handler(self, ctx: Context, ev: StartEvent) -> CodeActExecuteEvent | ReasoningLogicEvent:
        """
        协调规划和执行的主执行循环。

        Returns:
            包含执行结果的字典
        """
        logger.info(f"🚀 运行DroidAgent以实现目标: {self.goal}")
        ctx.write_event_to_stream(ev)

        self.step_counter = 0
        self.retry_counter = 0

        if not self.reasoning:
            logger.info(f"🔄 直接执行模式 - 执行目标: {self.goal}")
            task = Task(
                description=self.goal,
                status=self.task_manager.STATUS_PENDING,
                agent_type="Default"
            )

            return CodeActExecuteEvent(task=task, reflection=None)

        return ReasoningLogicEvent()
                
        
    @step
    async def finalize(self, ctx: Context, ev: FinalizeEvent) -> StopEvent:
        ctx.write_event_to_stream(ev)
        capture(
            DroidAgentFinalizeEvent(
                tasks=",".join([f"{t.agent_type}:{t.description}" for t in ev.task]),
                success=ev.success,
                output=ev.output,
                steps=ev.steps,
            )
        )
        flush()

        result = {
            "success": ev.success,
            # 已弃用。请使用output代替。
            "reason": ev.reason,
            "output": ev.output,
            "steps": ev.steps,
        }

        if self.trajectory and self.save_trajectories:
            self.trajectory.save_trajectory()

        return StopEvent(result)
    
    def handle_stream_event(self, ev: Event, ctx: Context):

        if isinstance(ev, EpisodicMemoryEvent):
            self.current_episodic_memory = ev.episodic_memory
            return

        if not isinstance(ev, StopEvent):
            ctx.write_event_to_stream(ev)
            
            if isinstance(ev, ScreenshotEvent):
                self.trajectory.screenshots.append(ev.screenshot)

            else:
                self.trajectory.events.append(ev)



