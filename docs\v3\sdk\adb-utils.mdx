---
title: Adb Utils
---

Device - High-level representation of an Android device.

<a id="droidrun.adb.device.Device"></a>

## Device

```python
class Device()
```

High-level representation of an Android device.

<a id="droidrun.adb.device.Device.__init__"></a>

#### Device.\_\_init\_\_

```python
def __init__(serial: str, adb: ADBWrapper)
```

Initialize device.

**Arguments**:

- `serial` - Device serial number
- `adb` - ADB wrapper instance

<a id="droidrun.adb.device.Device.serial"></a>

#### Device.serial

```python
def serial() -> str
```

Get device serial number.

<a id="droidrun.adb.device.Device.get_properties"></a>

#### Device.get\_properties

```python
async def get_properties() -> Dict[str, str]
```

Get all device properties.

<a id="droidrun.adb.device.Device.get_property"></a>

#### Device.get\_property

```python
async def get_property(name: str) -> str
```

Get a specific device property.

<a id="droidrun.adb.device.Device.model"></a>

#### Device.model

```python
async def model() -> str
```

Get device model.

<a id="droidrun.adb.device.Device.brand"></a>

#### Device.brand

```python
async def brand() -> str
```

Get device brand.

<a id="droidrun.adb.device.Device.android_version"></a>

#### Device.android\_version

```python
async def android_version() -> str
```

Get Android version.

<a id="droidrun.adb.device.Device.sdk_level"></a>

#### Device.sdk\_level

```python
async def sdk_level() -> str
```

Get SDK level.

<a id="droidrun.adb.device.Device.shell"></a>

#### Device.shell

```python
async def shell(command: str, timeout: float | None = None) -> str
```

Execute a shell command on the device.

<a id="droidrun.adb.device.Device.tap"></a>

#### Device.tap

```python
async def tap(x: int, y: int) -> None
```

Tap at coordinates.

**Arguments**:

- `x` - X coordinate
- `y` - Y coordinate

<a id="droidrun.adb.device.Device.swipe"></a>

#### Device.swipe

```python
async def swipe(
    start_x: int,
    start_y: int,
    end_x: int,
    end_y: int,
    duration_ms: int = 300
) -> None
```

Perform swipe gesture.

**Arguments**:

- `start_x` - Starting X coordinate
- `start_y` - Starting Y coordinate
- `end_x` - Ending X coordinate
- `end_y` - Ending Y coordinate
- `duration_ms` - Swipe duration in milliseconds

<a id="droidrun.adb.device.Device.input_text"></a>

#### Device.input\_text

```python
async def input_text(text: str) -> None
```

Input text.

**Arguments**:

- `text` - Text to input

<a id="droidrun.adb.device.Device.press_key"></a>

#### Device.press\_key

```python
async def press_key(keycode: int) -> None
```

Press a key.

**Arguments**:

- `keycode` - Android keycode to press

<a id="droidrun.adb.device.Device.start_activity"></a>

#### Device.start\_activity

```python
async def start_activity(
    package: str,
    activity: str = ".MainActivity",
    extras: Optional[Dict[str, str]] = None
) -> None
```

Start an app activity.

**Arguments**:

- `package` - Package name
- `activity` - Activity name
- `extras` - Intent extras

<a id="droidrun.adb.device.Device.start_app"></a>

#### Device.start\_app

```python
async def start_app(package: str, activity: str = "") -> str
```

Start an app on the device.

**Arguments**:

- `package` - Package name
- `activity` - Optional activity name (if empty, launches default activity)
  

**Returns**:

  Result message

<a id="droidrun.adb.device.Device.install_app"></a>

#### Device.install\_app

```python
async def install_app(
    apk_path: str,
    reinstall: bool = False,
    grant_permissions: bool = True
) -> str
```

Install an APK on the device.

**Arguments**:

- `apk_path` - Path to the APK file
- `reinstall` - Whether to reinstall if app exists
- `grant_permissions` - Whether to grant all requested permissions
  

**Returns**:

  Installation result

<a id="droidrun.adb.device.Device.uninstall_app"></a>

#### Device.uninstall\_app

```python
async def uninstall_app(package: str, keep_data: bool = False) -> str
```

Uninstall an app from the device.

**Arguments**:

- `package` - Package name to uninstall
- `keep_data` - Whether to keep app data and cache directories
  

**Returns**:

  Uninstallation result

<a id="droidrun.adb.device.Device.take_screenshot"></a>

#### Device.take\_screenshot

```python
async def take_screenshot(quality: int = 75) -> Tuple[str, bytes]
```

Take a screenshot of the device and compress it.

**Arguments**:

- `quality` - JPEG quality (1-100, lower means smaller file size)
  

**Returns**:

  Tuple of (local file path, screenshot data as bytes)

<a id="droidrun.adb.device.Device.list_packages"></a>

#### Device.list\_packages

```python
async def list_packages(include_system_apps: bool = False) -> List[str]
```

List installed packages on the device.

**Arguments**:

- `include_system_apps` - Whether to include system apps (default: False)
  

**Returns**:

  List of package names

Device Manager - Manages Android device connections.

<a id="droidrun.adb.manager.DeviceManager"></a>

## DeviceManager

```python
class DeviceManager()
```

Manages Android device connections.

<a id="droidrun.adb.manager.DeviceManager.__init__"></a>

#### DeviceManager.\_\_init\_\_

```python
def __init__(adb_path: Optional[str] = None)
```

Initialize device manager.

**Arguments**:

- `adb_path` - Path to ADB binary

<a id="droidrun.adb.manager.DeviceManager.list_devices"></a>

#### DeviceManager.list\_devices

```python
async def list_devices() -> List[Device]
```

List connected devices.

**Returns**:

  List of connected devices

<a id="droidrun.adb.manager.DeviceManager.get_device"></a>

#### DeviceManager.get\_device

```python
async def get_device(serial: str | None = None) -> Optional[Device]
```

Get a specific device.

**Arguments**:

- `serial` - Device serial number
  

**Returns**:

  Device instance if found, None otherwise

<a id="droidrun.adb.manager.DeviceManager.connect"></a>

#### DeviceManager.connect

```python
async def connect(host: str, port: int = 5555) -> Optional[Device]
```

Connect to a device over TCP/IP.

**Arguments**:

- `host` - Device IP address
- `port` - Device port
  

**Returns**:

  Connected device instance

<a id="droidrun.adb.manager.DeviceManager.disconnect"></a>

#### DeviceManager.disconnect

```python
async def disconnect(serial: str) -> bool
```

Disconnect from a device.

**Arguments**:

- `serial` - Device serial number
  

**Returns**:

  True if disconnected successfully

