#!/usr/bin/env python3
"""
验证 DroidRun 包的完整性和功能
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    print("🐍 检查 Python 版本...")
    
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 10:
        print("   ✅ Python 版本符合要求 (>=3.10)")
        return True
    else:
        print("   ❌ Python 版本不符合要求，需要 3.10 或更高版本")
        return False

def check_package_files():
    """检查包文件结构"""
    print("\n📁 检查包文件结构...")
    
    required_files = [
        "pyproject.toml",
        "setup.py", 
        "MANIFEST.in",
        "droidrun/__init__.py",
        "droidrun/tools/adb.py",
        "droidrun/agent/droid/droid_agent.py",
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"   ✅ {file}")
    
    if missing_files:
        print(f"   ❌ 缺少文件: {missing_files}")
        return False
    
    print("   ✅ 所有必要文件都存在")
    return True

def check_pyproject_config():
    """检查 pyproject.toml 配置"""
    print("\n⚙️ 检查 pyproject.toml 配置...")
    
    try:
        with open("pyproject.toml", "r", encoding="utf-8") as f:
            content = f.read()
        
        checks = [
            ('requires-python = ">=3.10"', "Python 版本要求"),
            ('name = "droidrun"', "包名"),
            ('version = "0.3.2"', "版本号"),
            ('[build-system]', "构建系统"),
            ('packages = ["droidrun"]', "包配置"),
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"   ✅ {desc}: {check}")
            else:
                print(f"   ❌ {desc}: 未找到 {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 读取配置文件失败: {e}")
        return False

def test_import():
    """测试导入功能"""
    print("\n🧪 测试导入功能...")
    
    try:
        import droidrun
        print(f"   ✅ 成功导入 droidrun (版本: {droidrun.__version__})")
        
        # 测试主要组件
        try:
            from droidrun import AdbTools, DroidAgent, set_language, Language
            print("   ✅ 成功导入主要组件")
            
            # 测试语言设置
            set_language(Language.CHINESE)
            print("   ✅ 语言设置功能正常")
            
            return True
            
        except ImportError as e:
            print(f"   ⚠️ 部分组件导入失败: {e}")
            return True  # 主包导入成功就算通过
            
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def check_build_capability():
    """检查构建能力"""
    print("\n🔨 检查构建能力...")
    
    # 检查是否存在构建脚本
    if os.path.exists("build_wheel.py"):
        print("   ✅ 找到构建脚本: build_wheel.py")
    else:
        print("   ⚠️ 未找到 build_wheel.py，可以使用 pip install . 安装")
    
    # 检查 dist 目录
    if os.path.exists("dist"):
        dist_files = list(Path("dist").glob("*.whl"))
        if dist_files:
            print(f"   ✅ 找到构建的包: {[f.name for f in dist_files]}")
        else:
            print("   ⚠️ dist 目录存在但没有 wheel 文件")
    else:
        print("   ℹ️ 尚未构建包，运行 python build_wheel.py 来构建")
    
    return True

def show_installation_commands():
    """显示安装命令"""
    print("\n📋 安装命令:")
    print("   # 开发模式安装（推荐）:")
    print("   pip install -e .")
    print()
    print("   # 构建并安装:")
    print("   python build_wheel.py")
    print("   pip install dist/droidrun-0.3.2-py3-none-any.whl --force-reinstall")
    print()
    print("   # 直接安装:")
    print("   pip install .")

def main():
    """主函数"""
    print("🎯 DroidRun 包验证工具")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 执行各项检查
    checks = [
        check_python_version,
        check_package_files,
        check_pyproject_config,
        test_import,
        check_build_capability,
    ]
    
    for check in checks:
        if not check():
            all_checks_passed = False
    
    # 显示结果
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("✅ 所有检查通过！DroidRun 包已准备就绪")
        print("\n🚀 您的 DroidRun 现在可以:")
        print("   • 作为标准 Python 包安装")
        print("   • 生成 .dist-info 目录结构")
        print("   • 被其他用户轻松使用")
        print("   • 支持开发模式安装")
    else:
        print("❌ 部分检查未通过，请检查上述问题")
    
    show_installation_commands()

if __name__ == "__main__":
    main()
