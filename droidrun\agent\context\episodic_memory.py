from dataclasses import dataclass, field
from droidrun.agent.context.agent_persona import <PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional

@dataclass
class EpisodicMemoryStep:
    chat_history: str
    response: str
    timestamp: float
    screenshot: Optional[bytes]

@dataclass 
class EpisodicMemory:
    persona: AgentPersona
    steps: List[EpisodicMemoryStep] = field(default_factory=list)