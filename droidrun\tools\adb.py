"""
UI操作 - 用于Android设备控制的核心UI交互工具。
"""

import os
import json
import time
import asyncio
import logging
from typing_extensions import Optional, Dict, Tuple, List, Any, Type, Self
from droidrun.adb.device import Device
from droidrun.adb.manager import DeviceManager
from droidrun.tools.tools import Tools

logger = logging.getLogger("droidrun-adb-tools")


class AdbTools(Tools):
    """用于Android设备控制的核心UI交互工具。"""

    def __init__(self, serial: str) -> None:
        """初始化AdbTools实例。

        参数:
            serial: 设备序列号
        """
        self.device_manager = DeviceManager()
        # 实例级别的可点击元素缓存（基于索引的点击）
        self.clickable_elements_cache: List[Dict[str, Any]] = []
        self.serial = serial
        self.last_screenshot = None
        self.reason = None
        self.success = None
        self.finished = False
        # 用于记住重要信息的内存存储
        self.memory: List[str] = []
        # 存储所有带时间戳的截图
        self.screenshots: List[Dict[str, Any]] = []

    @classmethod
    async def create(cls: Type[Self], serial: str = None) -> Self:
        """创建一个AdbTools实例。

        参数:
            serial: 可选的设备序列号。如果未提供，将使用找到的第一个设备。

        返回:
            AdbTools实例
        """
        if not serial:
            dvm = DeviceManager()
            devices = await dvm.list_devices()
            if not devices or len(devices) < 1:
                raise ValueError("未找到设备")
            serial = devices[0].serial

        return AdbTools(serial)

    def _get_device_serial(self) -> str:
        """从实例或环境变量获取设备序列号。"""
        # 首先尝试使用实例的序列号
        if self.serial:
            return self.serial

    async def _get_device(self) -> Optional[Device]:
        """使用实例的序列号或从环境变量获取设备实例。

        返回:
            设备实例，如果未找到则为None
        """
        serial = self._get_device_serial()
        if not serial:
            raise ValueError("未指定设备序列号 - 设置device_serial参数")

        device = await self.device_manager.get_device(serial)
        if not device:
            raise ValueError(f"未找到设备 {serial}")

        return device

    def _parse_content_provider_output(
        self, raw_output: str
    ) -> Optional[Dict[str, Any]]:
        """
        解析原始ADB内容提供者输出并提取JSON数据。

        参数:
            raw_output (str): 来自ADB内容查询命令的原始输出

        返回:
            dict: 解析的JSON数据，如果解析失败则为None
        """
        # ADB内容查询输出格式为："Row: 0 result={json_data}"
        # 我们需要提取"result="之后的JSON部分
        lines = raw_output.strip().split("\n")

        for line in lines:
            line = line.strip()

            # 查找包含"result="模式的行
            if "result=" in line:
                # 提取"result="之后的所有内容
                result_start = line.find("result=") + 7
                json_str = line[result_start:]

                try:
                    # 解析JSON字符串
                    json_data = json.loads(json_str)
                    return json_data
                except json.JSONDecodeError:
                    continue

            # 备用方案：尝试解析以{或[开头的行
            elif line.startswith("{") or line.startswith("["):
                try:
                    json_data = json.loads(line)
                    return json_data
                except json.JSONDecodeError:
                    continue

        # 如果在单独的行中没有找到有效的JSON，尝试整个输出
        try:
            json_data = json.loads(raw_output.strip())
            return json_data
        except json.JSONDecodeError:
            return None

    async def tap_by_index(self, index: int, serial: Optional[str] = None) -> str:
        """
        通过索引点击UI元素。

        此函数使用缓存的可点击元素
        找到具有给定索引的元素并点击其中心坐标。

        参数:
            index: 要点击的元素的索引

        返回:
            结果消息
        """

        def collect_all_indices(elements):
            """递归收集元素及其子元素的所有索引。"""
            indices = []
            for item in elements:
                if item.get("index") is not None:
                    indices.append(item.get("index"))
                # 如果存在子元素，则检查子元素
                children = item.get("children", [])
                indices.extend(collect_all_indices(children))
            return indices

        def find_element_by_index(elements, target_index):
            """递归查找具有给定索引的元素。"""
            for item in elements:
                if item.get("index") == target_index:
                    return item
                # 如果存在子元素，则检查子元素
                children = item.get("children", [])
                result = find_element_by_index(children, target_index)
                if result:
                    return result
            return None

        try:
            # 检查是否有缓存的元素
            if not self.clickable_elements_cache:
                return "错误：没有缓存的UI元素。请先调用get_state。"

            # 查找具有给定索引的元素（包括子元素中的）
            element = find_element_by_index(self.clickable_elements_cache, index)

            if not element:
                # 列出可用的索引以帮助用户
                indices = sorted(collect_all_indices(self.clickable_elements_cache))
                indices_str = ", ".join(str(idx) for idx in indices[:20])
                if len(indices) > 20:
                    indices_str += f"... 还有 {len(indices) - 20} 个"

                return f"错误：未找到索引为 {index} 的元素。可用索引：{indices_str}"

            # 获取元素的边界
            bounds_str = element.get("bounds")
            if not bounds_str:
                element_text = element.get("text", "无文本")
                element_type = element.get("type", "未知")
                element_class = element.get("className", "未知类")
                return f"错误：索引为 {index} 的元素（'{element_text}'，{element_class}，类型：{element_type}）没有边界，无法点击"

            # 解析边界（格式："left,top,right,bottom"）
            try:
                left, top, right, bottom = map(int, bounds_str.split(","))
            except ValueError:
                return f"错误：索引为 {index} 的元素的边界格式无效：{bounds_str}"

            # 计算元素的中心
            x = (left + right) // 2
            y = (top + bottom) // 2

            # 获取设备并在坐标处点击
            if serial:
                device = await self.device_manager.get_device(serial)
                if not device:
                    return f"错误：未找到设备 {serial}"
            else:
                device = await self._get_device()

            await device.tap(x, y)

            # 添加小延迟以允许UI更新
            await asyncio.sleep(0.5)

            # 创建描述性响应
            response_parts = []
            response_parts.append(f"点击了索引为 {index} 的元素")
            response_parts.append(f"文本：'{element.get('text', '无文本')}'")
            response_parts.append(f"类：{element.get('className', '未知类')}")
            response_parts.append(f"类型：{element.get('type', '未知')}")

            # 如果存在子元素，添加子元素信息
            children = element.get("children", [])
            if children:
                child_texts = [
                    child.get("text") for child in children if child.get("text")
                ]
                if child_texts:
                    response_parts.append(f"包含文本：{' | '.join(child_texts)}")

            response_parts.append(f"坐标：({x}, {y})")

            return " | ".join(response_parts)
        except ValueError as e:
            return f"错误：{str(e)}"

    # 将旧的tap函数重命名为tap_by_coordinates以实现向后兼容
    async def tap_by_coordinates(self, x: int, y: int) -> bool:
        """
        在设备屏幕上的特定坐标处点击。

        参数:
            x: X坐标
            y: Y坐标

        返回:
            表示成功或失败的布尔值
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            await device.tap(x, y)
            print(f"在坐标 ({x}, {y}) 处点击")
            return True
        except ValueError as e:
            print(f"错误：{str(e)}")
            return False

    # 用新函数替换旧的tap函数
    async def tap(self, index: int) -> str:
        """
        通过索引点击UI元素。

        此函数使用上次get_clickables调用的缓存可点击元素
        找到具有给定索引的元素并点击其中心坐标。

        参数:
            index: 要点击的元素的索引

        返回:
            结果消息
        """
        return await self.tap_by_index(index)

    async def swipe(
        self, start_x: int, start_y: int, end_x: int, end_y: int, duration_ms: int = 300
    ) -> bool:
        """
        在设备屏幕上执行直线滑动手势。
        要执行按住（长按），请将起始和结束坐标设置为相同的值，并根据需要增加持续时间。
        参数:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration_ms: 滑动持续时间（毫秒）
        返回:
            表示成功或失败的布尔值
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            await device.swipe(start_x, start_y, end_x, end_y, duration_ms)
            await asyncio.sleep(1)
            print(
                f"从 ({start_x}, {start_y}) 滑动到 ({end_x}, {end_y})，持续 {duration_ms}毫秒"
            )
            return True
        except ValueError as e:
            print(f"错误：{str(e)}")
            return False

    async def drag_and_drop(
        self, start_x: int, start_y: int, end_x: int, end_y: int, hold_duration_ms: int = 800
    ) -> bool:
        """
        执行拖拽操作，适用于列表项重排序等需要长按拖动的场景。
        首先尝试使用高级的原生输入事件方法，如果失败则回退到简单的滑动方法。

        参数:
            start_x: 起始X坐标（要拖拽的元素中心）
            start_y: 起始Y坐标（要拖拽的元素中心）
            end_x: 目标X坐标（放置位置）
            end_y: 目标Y坐标（放置位置）
            hold_duration_ms: 长按持续时间（毫秒），用于激活拖拽模式

        返回:
            表示成功或失败的布尔值
        """
        # 首先尝试高级拖拽方法
        try:
            return await self.drag_and_drop_advanced(start_x, start_y, end_x, end_y, hold_duration_ms)
        except Exception as e:
            print(f"高级拖拽方法失败: {e}")
            print("尝试简单拖拽方法...")

        # 回退到简单的滑动方法
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            # 计算拖拽距离和调整持续时间
            distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5

            # 使用更长的持续时间来确保拖拽被正确识别
            # 对于短距离拖拽，使用至少1.5秒；对于长距离，按比例增加
            drag_duration_ms = max(1500, int(distance * 3))

            print(f"简单拖拽：从 ({start_x}, {start_y}) 拖动到 ({end_x}, {end_y})")
            print(f"拖拽距离: {distance:.1f}px，持续时间: {drag_duration_ms}ms")

            # 执行单一的长时间滑动手势来模拟拖拽
            await device.swipe(start_x, start_y, end_x, end_y, drag_duration_ms)

            # 等待UI更新和动画完成
            await asyncio.sleep(1.5)

            print(f"简单拖拽完成：已将元素从 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})")
            return True

        except ValueError as e:
            print(f"拖拽操作错误：{str(e)}")
            return False
        except Exception as e:
            print(f"拖拽操作异常：{str(e)}")
            return False

    async def drag_and_drop_advanced(
        self, start_x: int, start_y: int, end_x: int, end_y: int, hold_duration_ms: int = 1200
    ) -> bool:
        """
        使用Android原生输入事件执行高级拖拽操作。
        这种方法模拟真实的触摸事件序列：按下 -> 移动 -> 释放

        参数:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 目标X坐标
            end_y: 目标Y坐标
            hold_duration_ms: 长按持续时间（毫秒）

        返回:
            表示成功或失败的布尔值
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            print(f"高级拖拽：从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
            print(f"长按时间: {hold_duration_ms}ms")

            # 使用原生输入事件序列
            # 1. 按下 (ACTION_DOWN)
            await device.shell(f"input motionevent DOWN {start_x} {start_y}")

            # 2. 长按等待（激活拖拽模式）- 增加到1.2秒确保激活
            await asyncio.sleep(hold_duration_ms / 1000.0)

            # 3. 计算移动步骤 - 进一步优化速度
            steps = 2  # 进一步减少步骤数，提高移动速度
            dx = (end_x - start_x) / steps
            dy = (end_y - start_y) / steps

            # 计算超出目标位置的偏移量（用于列表重排序）
            distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
            overshoot_pixels = min(55, max(35, int(distance * 0.1)))  # 增加15px：10-30px → 25-45px

            # 如果是垂直拖拽（列表重排序），向拖拽方向继续超出
            if abs(dy) > abs(dx):  # 垂直拖拽
                # 修正逻辑：向上拖拽继续向上超出，向下拖拽继续向下超出
                overshoot_y = -overshoot_pixels if dy < 0 else overshoot_pixels
                final_x, final_y = end_x, end_y + overshoot_y
            else:  # 水平拖拽
                # 向左拖拽继续向左超出，向右拖拽继续向右超出
                overshoot_x = -overshoot_pixels if dx < 0 else overshoot_pixels
                final_x, final_y = end_x + overshoot_x, end_y

            print(f"拖拽路径：{steps}步，每步移动 ({dx:.1f}, {dy:.1f})")
            print(f"目标位置: ({end_x}, {end_y}), 超出位置: ({final_x}, {final_y}), 超出量: {overshoot_pixels}px")

            # 4. 快速移动到目标位置 (ACTION_MOVE)
            for i in range(1, steps + 1):
                current_x = int(start_x + dx * i)
                current_y = int(start_y + dy * i)
                await device.shell(f"input motionevent MOVE {current_x} {current_y}")
                await asyncio.sleep(0.01)  # 进一步减少延迟，提高移动速度

            # 5. 移动到超出位置（确保触发插入）
            await device.shell(f"input motionevent MOVE {final_x} {final_y}")
            await asyncio.sleep(0.5)  # 在超出位置停顿0.2秒，确保系统识别拖拽意图

            # 6. 释放 (ACTION_UP) - 在最终目标位置释放
            await device.shell(f"input motionevent UP {end_x} {end_y}")

            # 等待UI更新
            await asyncio.sleep(1.0)

            print(f"高级拖拽完成：已将元素从 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})，超出到 ({final_x}, {final_y})")
            return True

        except Exception as e:
            print(f"高级拖拽操作异常：{str(e)}")
            # 如果高级方法失败，回退到简单方法
            print("回退到简单拖拽方法...")
            # 避免无限递归，直接使用简单的滑动方法
            try:
                device = await self._get_device()
                distance = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
                drag_duration_ms = max(1500, int(distance * 3))
                await device.swipe(start_x, start_y, end_x, end_y, drag_duration_ms)
                await asyncio.sleep(1.5)
                print(f"回退拖拽完成：已将元素从 ({start_x}, {start_y}) 拖拽到 ({end_x}, {end_y})")
                return True
            except Exception as fallback_error:
                print(f"回退拖拽也失败：{fallback_error}")
                return False

    async def input_text(self, text: str, serial: Optional[str] = None) -> str:
        """
        在设备上输入文本。
        在输入文本之前，请始终确保聚焦元素不为None。

        参数:
            text: 要输入的文本。可以包含空格、换行符和特殊字符，包括非ASCII字符。

        返回:
            结果消息
        """
        try:
            if serial:
                device = await self.device_manager.get_device(serial)
                if not device:
                    return f"错误：未找到设备 {serial}"
            else:
                device = await self._get_device()

            # # 保存当前键盘
            # original_ime = await device._adb.shell(
            #     device._serial, "settings get secure default_input_method"
            # )
            # original_ime = original_ime.strip()

            # # 启用Droidrun键盘
            # await device._adb.shell(
            #     device._serial, "ime enable com.droidrun.portal/.DroidrunKeyboardIME"
            # )

            # # 将Droidrun键盘设置为默认
            # await device._adb.shell(
            #     device._serial, "ime set com.droidrun.portal/.DroidrunKeyboardIME"
            # )

            # 等待键盘切换
            await asyncio.sleep(1)

            # 将文本编码为Base64
            import base64

            encoded_text = base64.b64encode(text.encode()).decode()

            cmd = f'content insert --uri "content://com.droidrun.portal/keyboard/input" --bind base64_text:s:"{encoded_text}"'
            await device._adb.shell(device._serial, cmd)

            # 等待文本输入完成
            await asyncio.sleep(0.5)

            # 恢复原始键盘
            # if original_ime and "com.droidrun.portal" not in original_ime:
            #     await device._adb.shell(device._serial, f"ime set {original_ime}")

            return f"文本输入完成：{text[:50]}{'...' if len(text) > 50 else ''}"
        except ValueError as e:
            return f"错误：{str(e)}"
        except Exception as e:
            return f"发送文本输入时出错：{str(e)}"

    async def back(self) -> str:
        """
        在当前视图中返回。
        这会按下Android返回按钮。
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            await device.press_key(3)
            return f"按下了返回键"
        except ValueError as e:
            return f"错误：{str(e)}"

    async def press_key(self, keycode: int) -> str:
        """
        在Android设备上按键。

        常用键码：
        - 3: HOME（主页）
        - 4: BACK（返回）
        - 66: ENTER（回车）
        - 67: DELETE（删除）

        参数:
            keycode: 要按下的Android键码
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            key_names = {
                66: "ENTER",
                4: "BACK",
                3: "HOME",
                67: "DELETE",
            }
            key_name = key_names.get(keycode, str(keycode))

            await device.press_key(keycode)
            return f"按下了键 {key_name}"
        except ValueError as e:
            return f"错误：{str(e)}"

    async def start_app(self, package: str, activity: str = "") -> str:
        """
        在设备上启动应用程序。

        参数:
            package: 包名（例如："com.android.settings"）
            activity: 可选的活动名称
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            result = await device.start_app(package, activity)
            return result
        except ValueError as e:
            return f"错误：{str(e)}"

    async def install_app(
        self, apk_path: str, reinstall: bool = False, grant_permissions: bool = True
    ) -> str:
        """
        在设备上安装应用程序。

        参数:
            apk_path: APK文件的路径
            reinstall: 如果应用程序存在是否重新安装
            grant_permissions: 是否授予所有权限
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    return f"错误：未找到设备 {self.serial}"
            else:
                device = await self._get_device()

            if not os.path.exists(apk_path):
                return f"错误：在 {apk_path} 处未找到APK文件"

            result = await device.install_app(apk_path, reinstall, grant_permissions)
            return result
        except ValueError as e:
            return f"错误：{str(e)}"

    async def take_screenshot(self) -> Tuple[str, bytes]:
        """
        对设备进行截图。
        此函数捕获当前屏幕并在下一条消息中将截图添加到上下文中。
        还将截图与时间戳一起存储在截图列表中，以便稍后创建GIF。
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    raise ValueError(f"未找到设备 {self.serial}")
            else:
                device = await self._get_device()
            screen_tuple = await device.take_screenshot()
            self.last_screenshot = screen_tuple[1]

            # 存储带时间戳的截图
            self.screenshots.append(
                {
                    "timestamp": time.time(),
                    "image_data": screen_tuple[1],
                    "format": screen_tuple[0],  # 通常是'PNG'
                }
            )
            return screen_tuple
        except ValueError as e:
            raise ValueError(f"截图时出错：{str(e)}")

    async def list_packages(self, include_system_apps: bool = False) -> List[str]:
        """
        列出设备上已安装的包。

        参数:
            include_system_apps: 是否包含系统应用（默认：False）

        返回:
            包名列表
        """
        try:
            if self.serial:
                device = await self.device_manager.get_device(self.serial)
                if not device:
                    raise ValueError(f"未找到设备 {self.serial}")
            else:
                device = await self._get_device()

            return await device.list_packages(include_system_apps)
        except ValueError as e:
            raise ValueError(f"列出包时出错：{str(e)}")

    def complete(self, success: bool, reason: str = ""):
        """
        将任务标记为已完成。

        参数:
            success: 指示任务是否成功。
            reason: 失败/成功的原因
        """
        if success:
            self.success = True
            self.reason = reason or "任务成功完成。"
            self.finished = True
        else:
            self.success = False
            if not reason:
                raise ValueError("如果success为False，则需要失败原因。")
            self.reason = reason
            self.finished = True

    async def remember(self, information: str) -> str:
        """
        存储重要信息以供将来上下文使用。

        此信息将被提取并包含在您的下一步中，以在交互中保持上下文。
        将此用于关键事实、观察或应影响未来决策的用户偏好。

        参数:
            information: 要记住的信息

        返回:
            确认消息
        """
        if not information or not isinstance(information, str):
            return "错误：请提供有效的信息来记住。"

        # 将信息添加到内存中
        self.memory.append(information.strip())

        # 限制内存大小以防止上下文溢出（保留最近的项目）
        max_memory_items = 10
        if len(self.memory) > max_memory_items:
            self.memory = self.memory[-max_memory_items:]

        return f"已记住：{information}"

    def get_memory(self) -> List[str]:
        """
        检索所有存储的内存项目。

        返回:
            存储的内存项目列表
        """
        return self.memory.copy()

    async def get_state(self, serial: Optional[str] = None) -> Dict[str, Any]:
        """
        使用组合的/state端点在单次调用中获取a11y树和手机状态。

        参数:
            serial: 可选的设备序列号

        返回:
            包含'a11y_tree'和'phone_state'数据的字典
        """

        try:
            if serial:
                device = await self.device_manager.get_device(serial)
                if not device:
                    raise ValueError(f"未找到设备 {serial}")
            else:
                device = await self._get_device()

            adb_output = await device._adb.shell(
                device._serial,
                "content query --uri content://com.droidrun.portal/state",
            )

            state_data = self._parse_content_provider_output(adb_output)

            if state_data is None:
                return {
                    "error": "解析错误",
                    "message": "无法从ContentProvider响应中解析状态数据",
                }

            if isinstance(state_data, dict) and "data" in state_data:
                data_str = state_data["data"]
                try:
                    combined_data = json.loads(data_str)
                except json.JSONDecodeError:
                    return {
                        "error": "解析错误",
                        "message": "无法从ContentProvider数据字段解析JSON数据",
                    }
            else:
                return {
                    "error": "格式错误",
                    "message": f"意外的状态数据格式：{type(state_data)}",
                }

            # 验证a11y_tree和phone_state都存在
            if "a11y_tree" not in combined_data:
                return {
                    "error": "缺少数据",
                    "message": "在组合状态数据中未找到a11y_tree",
                }

            if "phone_state" not in combined_data:
                return {
                    "error": "缺少数据",
                    "message": "在组合状态数据中未找到phone_state",
                }

            # 从所有a11y_tree元素中过滤掉"type"属性
            elements = combined_data["a11y_tree"]
            filtered_elements = []
            for element in elements:
                # 创建不包含"type"属性的元素副本
                filtered_element = {k: v for k, v in element.items() if k != "type"}

                # 如果存在子元素，也过滤子元素
                if "children" in filtered_element:
                    filtered_element["children"] = [
                        {k: v for k, v in child.items() if k != "type"}
                        for child in filtered_element["children"]
                    ]

                filtered_elements.append(filtered_element)

            self.clickable_elements_cache = filtered_elements

            return {
                "a11y_tree": filtered_elements,
                "phone_state": combined_data["phone_state"],
            }

        except Exception as e:
            return {
                "error": str(e),
                "message": f"获取组合状态时出错：{str(e)}",
            }


if __name__ == "__main__":

    async def main():
        tools = await AdbTools.create()
        print(tools.serial)

    asyncio.run(main())
