from posthog import Posthog
from pathlib import Path
from uuid import uuid4
import os
import logging
from .events import TelemetryEvent

logger = logging.getLogger("droidrun-telemetry")
droidrun_logger = logging.getLogger("droidrun")

PROJECT_API_KEY = "phc_XyD3HKIsetZeRkmnfaBughs8fXWYArSUFc30C0HmRiO"
HOST = "https://eu.i.posthog.com"
USER_ID_PATH = Path.home() / ".droidrun" / "user_id"
RUN_ID = str(uuid4())

TELEMETRY_ENABLED_MESSAGE = "🕵️  匿名遥测已启用。更多信息请参见 https://docs.droidrun.ai/v3/guides/telemetry。"
TELEMETRY_DISABLED_MESSAGE = "🛑 匿名遥测已禁用。考虑将 DROIDRUN_TELEMETRY_ENABLED 环境变量设置为 'true' 以启用遥测并帮助我们改进 DroidRun。"

posthog = Posthog(
    project_api_key=PROJECT_API_KEY,
    host=HOST,
    disable_geoip=False,
)


def is_telemetry_enabled():
    telemetry_enabled = os.environ.get("DROIDRUN_TELEMETRY_ENABLED", "true")
    enabled = telemetry_enabled.lower() in ["true", "1", "yes", "y"]
    logger.debug(f"Telemetry enabled: {enabled}")
    return enabled


def print_telemetry_message():
    if is_telemetry_enabled():
        droidrun_logger.info(TELEMETRY_ENABLED_MESSAGE)

    else:
        droidrun_logger.info(TELEMETRY_DISABLED_MESSAGE)


# 在导入时打印遥测消息
print_telemetry_message()


def get_user_id() -> str:
    try:
        if not USER_ID_PATH.exists():
            USER_ID_PATH.touch()
            USER_ID_PATH.write_text(str(uuid4()))
        logger.debug(f"用户ID: {USER_ID_PATH.read_text()}")
        return USER_ID_PATH.read_text()
    except Exception as e:
        logger.error(f"获取用户ID时出错: {e}")
        return "unknown"


def capture(event: TelemetryEvent):
    try:
        if not is_telemetry_enabled():
            logger.debug(f"遥测已禁用，跳过捕获事件 {event}")
            return
        event_name = type(event).__name__
        event_data = event.model_dump()
        properties = {
            "run_id": RUN_ID,
            **event_data,
        }

        # posthog.capture(event_name, distinct_id=get_user_id(), properties=properties)
        logger.debug(f"已捕获事件: {event_name}，属性: {event}")
    except Exception as e:
        logger.error(f"捕获事件时出错: {e}")


def flush():
    try:
        if not is_telemetry_enabled():
            logger.debug(f"遥测已禁用，跳过刷新")
            return
        # posthog.flush()
        logger.debug(f"已刷新遥测数据")
    except Exception as e:
        logger.error(f"刷新遥测数据时出错: {e}")
