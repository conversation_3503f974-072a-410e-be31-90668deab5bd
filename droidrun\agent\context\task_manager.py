import os
from typing import List, Dict
from dataclasses import dataclass
import copy

@dataclass
class Task:
    """
    表示具有属性的单个任务。
    """
    description: str
    status: str
    agent_type: str


class TaskManager:
    """
    为代理管理任务列表，每个任务都有状态和分配的专门代理。
    """
    STATUS_PENDING = "pending"      
    STATUS_COMPLETED = "completed"
    STATUS_FAILED = "failed"

    VALID_STATUSES = {
        STATUS_PENDING,
        STATUS_COMPLETED,
        STATUS_FAILED
    }
    def __init__(self):
        """初始化空任务列表。"""
        self.tasks: List[Task] = []
        self.goal_completed = False
        self.message = None
        self.task_history = []
        self.file_path = os.path.join(os.path.dirname(__file__), "todo.txt")

    def get_all_tasks(self) -> List[Task]:
        return self.tasks
        
    def get_task_history(self):
        return self.task_history

    def complete_task(self, task: Task):
        task = copy.deepcopy(task)
        task.status = self.STATUS_COMPLETED
        self.task_history.append(task)

    def fail_task(self, task: Task):
        task = copy.deepcopy(task)
        task.status = self.STATUS_FAILED
        self.task_history.append(task)

    def get_completed_tasks(self) -> list[dict]:
        return [task for task in self.task_history if task.status == self.STATUS_COMPLETED]

    def get_failed_tasks(self) -> list[dict]:
        return [task for task in self.task_history if task.status == self.STATUS_FAILED]


    def save_to_file(self):
        """将当前任务列表保存到Markdown文件。"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                for i, task in enumerate(self.tasks, 1):
                    f.write(f"任务 {i}: {task.description}\n")
                    f.write(f"状态: {task.status}\n")
                    f.write(f"代理: {task.agent_type}\n")
                    f.write("-" * 40 + "\n")
        except Exception as e:
            print(f"保存任务到文件时出错: {e}")



    def set_tasks_with_agents(self, task_assignments: List[Dict[str, str]]):
        """
        清除当前任务列表并设置新任务及其分配的代理。

        Args:
            task_assignments: 字典列表，每个字典包含：
                            - 'task': 任务描述字符串
                            - 'agent': 代理类型

        Example:
            task_manager.set_tasks_with_agents([
                {'task': '打开Gmail应用', 'agent': 'AppStarterExpert'},
                {'task': '导航到撰写邮件', 'agent': 'UIExpert'}
            ])
        """
        try:
            self.tasks = []
            for i, assignment in enumerate(task_assignments):
                if not isinstance(assignment, dict) or 'task' not in assignment:
                    raise ValueError(f"每个任务分配必须是包含'task'键的字典，索引 {i}。")

                task_description = assignment['task']
                if not isinstance(task_description, str) or not task_description.strip():
                    raise ValueError(f"任务描述必须是非空字符串，索引 {i}。")

                agent_type = assignment.get('agent', 'Default')
                
                task_obj = Task(
                    description=task_description.strip(),
                    status=self.STATUS_PENDING,
                    agent_type=agent_type
                )
                
                self.tasks.append(task_obj)
            
            print(f"已设置带代理的任务: 添加了 {len(self.tasks)} 个任务。")
            self.save_to_file()
        except Exception as e:
            print(f"设置带代理的任务时出错: {e}")

    def complete_goal(self, message: str):
        """
        将目标标记为已完成，无论任务完成是成功还是失败都使用此方法。
        无论结果如何，当任务完成时都应调用此方法。

        Args:
            message: 要记录的消息。
        """
        self.goal_completed = True
        self.message = message
        print(f"目标完成: {message}")
