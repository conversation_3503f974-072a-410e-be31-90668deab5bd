import logging
import re
import time
import asyncio
import json
import os
from typing import List, Optional, Tuple, Union
from llama_index.core.base.llms.types import ChatMessage, ChatResponse
from llama_index.core.prompts import PromptTemplate
from llama_index.core.llms.llm import LLM
from llama_index.core.workflow import Workflow, StartEvent, StopEvent, Context, step
from llama_index.core.memory import Memory
from droidrun.agent.codeact.events import (
    TaskInputEvent,
    TaskEndEvent,
    TaskExecutionEvent,
    TaskExecutionResultEvent,
    TaskThinkingEvent,
    EpisodicMemoryEvent,
)
from droidrun.agent.common.events import ScreenshotEvent
from droidrun.agent.utils import chat_utils
from droidrun.agent.utils.executer import SimpleCodeExecutor
from droidrun.agent.codeact.prompts import (
    DEFAULT_CODE_ACT_USER_PROMPT,
    DEFAULT_NO_THOUGHTS_PROMPT,
)

from droidrun.agent.context.episodic_memory import EpisodicMemory, EpisodicMemoryStep
from droidrun.tools import Tools
from typing import Optional, Dict, Tuple, List, Any, Callable
from droidrun.agent.context.agent_persona import AgentPersona

logger = logging.getLogger("droidrun")


class CodeActAgent(Workflow):
    """
    一个使用类似ReAct循环（思考 -> 代码 -> 观察）的代理，
    用于解决需要代码执行的问题。它从Markdown块中提取代码，
    并使用特定的步骤类型进行跟踪。
    """

    def __init__(
        self,
        llm: LLM,
        persona: AgentPersona,
        vision: bool,
        tools_instance: "Tools",
        all_tools_list: Dict[str, Callable[..., Any]],
        max_steps: int = 5,
        debug: bool = False,
        *args,
        **kwargs,
    ):
        # 使用assert而不是if
        assert llm, "必须提供llm。"
        super().__init__(*args, **kwargs)

        self.llm = llm
        self.max_steps = max_steps

        self.user_prompt = persona.user_prompt
        self.no_thoughts_prompt = None

        self.vision = vision

        self.chat_memory = None
        self.episodic_memory = EpisodicMemory(persona=persona)
        self.remembered_info = None

        self.goal = None
        self.steps_counter = 0
        self.code_exec_counter = 0
        self.debug = debug

        self.tools = tools_instance

        self.tool_list = {}

        for tool_name in persona.allowed_tools:
            if tool_name in all_tools_list:
                self.tool_list[tool_name] = all_tools_list[tool_name]

        self.tool_descriptions = chat_utils.parse_tool_descriptions(self.tool_list)

        self.system_prompt_content = persona.system_prompt.format(
            tool_descriptions=self.tool_descriptions
        )
        self.system_prompt = ChatMessage(
            role="system", content=self.system_prompt_content
        )

        self.required_context = persona.required_context

        self.executor = SimpleCodeExecutor(
            loop=asyncio.get_event_loop(),
            locals={},
            tools=self.tool_list,
            globals={"__builtins__": __builtins__},
        )

        logger.info("✅ CodeActAgent初始化成功。")

    @step
    async def prepare_chat(self, ctx: Context, ev: StartEvent) -> TaskInputEvent:
        """从用户输入准备聊天历史。"""
        logger.info("💬 正在为任务执行准备聊天...")

        self.chat_memory: Memory = await ctx.get(
            "chat_memory", default=Memory.from_defaults()
        )

        user_input = ev.get("input", default=None)
        assert user_input, "用户输入不能为空。"

        if ev.remembered_info:
            self.remembered_info = ev.remembered_info

        logger.debug("  - 正在将目标添加到内存。")
        goal = user_input
        self.user_message = ChatMessage(
            role="user",
            content=PromptTemplate(
                self.user_prompt or DEFAULT_CODE_ACT_USER_PROMPT
            ).format(goal=goal),
        )
        self.no_thoughts_prompt = ChatMessage(
            role="user",
            content=PromptTemplate(DEFAULT_NO_THOUGHTS_PROMPT).format(goal=goal),
        )


        await self.chat_memory.aput(self.user_message)

        await ctx.set("chat_memory", self.chat_memory)
        input_messages = self.chat_memory.get_all()
        return TaskInputEvent(input=input_messages)

    @step
    async def handle_llm_input(
        self, ctx: Context, ev: TaskInputEvent
    ) -> TaskThinkingEvent | TaskEndEvent:
        """处理LLM输入。"""
        chat_history = ev.input
        assert len(chat_history) > 0, "聊天历史不能为空。"
        ctx.write_event_to_stream(ev)

        if self.steps_counter >= self.max_steps:
            ev = TaskEndEvent(
                success=False,
                reason=f"已达到最大步骤数 {self.max_steps} 步",
            )
            ctx.write_event_to_stream(ev)
            return ev

        self.steps_counter += 1
        logger.info(f"🧠 步骤 {self.steps_counter}: 思考中...")

        model = self.llm.class_name()

        if "remember" in self.tool_list and self.remembered_info:
            await ctx.set("remembered_info", self.remembered_info)
            chat_history = await chat_utils.add_memory_block(self.remembered_info, chat_history)

        for context in self.required_context:
            if model == "DeepSeek":
                logger.warning(
                    "[yellow]DeepSeek不支持图像。正在禁用屏幕截图[/]"
                )
            elif self.vision == True and context == "screenshot":
                screenshot = (await self.tools.take_screenshot())[1]
                ctx.write_event_to_stream(ScreenshotEvent(screenshot=screenshot))

                await ctx.set("screenshot", screenshot)
                chat_history = await chat_utils.add_screenshot_image_block(screenshot, chat_history)

            if context == "ui_state":
                try:
                    state = await self.tools.get_state()
                    await ctx.set("ui_state", state["a11y_tree"])
                    chat_history = await chat_utils.add_ui_text_block(
                        state["a11y_tree"], chat_history
                    )
                    chat_history = await chat_utils.add_phone_state_block(state["phone_state"], chat_history)
                except Exception as e:
                    logger.warning(f"⚠️ 从连接的设备检索状态时出错。无障碍服务是否已启用？")


            if context == "packages":
                chat_history = await chat_utils.add_packages_block(
                    await self.tools.list_packages(include_system_apps=True),
                    chat_history,
                )

        response = await self._get_llm_response(ctx, chat_history)
        if response is None:
            return TaskEndEvent(
                success=False, reason="LLM响应为None。这是一个严重错误。"
            )

        await self.chat_memory.aput(response.message)

        code, thoughts = chat_utils.extract_code_and_thought(response.message.content)

        event = TaskThinkingEvent(thoughts=thoughts, code=code)
        ctx.write_event_to_stream(event)
        return event

    @step
    async def handle_llm_output(
        self, ctx: Context, ev: TaskThinkingEvent
    ) -> Union[TaskExecutionEvent, TaskInputEvent]:
        """处理LLM输出。"""
        logger.debug("⚙️ 正在处理LLM输出...")
        code = ev.code
        thoughts = ev.thoughts

        if not thoughts:
            logger.warning(
                "🤔 LLM提供了代码但没有思考过程。正在添加提醒提示。"
            )
            await self.chat_memory.aput(self.no_thoughts_prompt)
        else:
            logger.info(f"🤔 推理: {thoughts}")

        if code:
            return TaskExecutionEvent(code=code)
        else:
            message = ChatMessage(
                role="user",
                content="未提供代码。如果您想将任务标记为完成（无论失败还是成功），请在代码块 ```python\n``` 中使用 complete(success:bool, reason:str) 函数。",
            )
            await self.chat_memory.aput(message)
            return TaskInputEvent(input=self.chat_memory.get_all())

    @step
    async def execute_code(
        self, ctx: Context, ev: TaskExecutionEvent
    ) -> Union[TaskExecutionResultEvent, TaskEndEvent]:
        """执行代码并返回结果。"""
        code = ev.code
        assert code, "代码不能为空。"
        logger.info(f"⚡ 正在执行动作...")
        logger.debug(f"要执行的代码:\n```python\n{code}\n```")

        try:
            self.code_exec_counter += 1
            result = await self.executor.execute(ctx, code)
            logger.info(f"💡 代码执行成功。结果: {result}")

            if self.tools.finished == True:
                logger.debug("  - 任务完成。")
                event = TaskEndEvent(
                    success=self.tools.success, reason=self.tools.reason
                )
                ctx.write_event_to_stream(event)
                return event

            self.remembered_info = self.tools.memory

            event = TaskExecutionResultEvent(output=str(result))
            ctx.write_event_to_stream(event)
            return event

        except Exception as e:
            logger.error(f"💥 动作失败: {e}")
            if self.debug:
                logger.error("异常详情:", exc_info=True)
            error_message = f"执行期间出错: {e}"

            event = TaskExecutionResultEvent(output=error_message)
            ctx.write_event_to_stream(event)
            return event

    @step
    async def handle_execution_result(
        self, ctx: Context, ev: TaskExecutionResultEvent
    ) -> TaskInputEvent:
        """处理执行结果。目前它只是返回InputEvent。"""
        logger.debug("📊 正在处理执行结果...")
        # 从事件中获取输出
        output = ev.output
        if output is None:
            output = "代码已执行，但未产生输出。"
            logger.warning("  - 执行未产生输出。")
        else:
            logger.debug(
                f"  - 执行输出: {output[:100]}..."
                if len(output) > 100
                else f"  - 执行输出: {output}"
            )
        # 将输出作为用户消息（观察）添加到内存中
        observation_message = ChatMessage(
            role="user", content=f"执行结果:\n```\n{output}\n```"
        )
        await self.chat_memory.aput(observation_message)

        return TaskInputEvent(input=self.chat_memory.get_all())

    @step
    async def finalize(self, ev: TaskEndEvent, ctx: Context) -> StopEvent:
        """完成工作流。"""
        self.tools.finished = False
        await ctx.set("chat_memory", self.chat_memory)

        # 将最终状态观察添加到情节记忆中
        await self._add_final_state_observation(ctx)
        
        result = {}
        result.update(
            {
                "success": ev.success,
                "reason": ev.reason,
                "output": ev.reason,
                "codeact_steps": self.steps_counter,
                "code_executions": self.code_exec_counter,
            }
        )

        ctx.write_event_to_stream(
            EpisodicMemoryEvent(episodic_memory=self.episodic_memory)
        )

        return StopEvent(result)

    async def _get_llm_response(
        self, ctx: Context, chat_history: List[ChatMessage]
    ) -> ChatResponse | None:
        logger.debug("🔍 正在获取LLM响应...")
        messages_to_send = [self.system_prompt] + chat_history
        messages_to_send = [chat_utils.message_copy(msg) for msg in messages_to_send]
        try:
            response = await self.llm.achat(messages=messages_to_send)
            logger.debug("🔍 已接收LLM响应。")

            filtered_chat_history = []
            for msg in chat_history:
                filtered_msg = chat_utils.message_copy(msg)
                if hasattr(filtered_msg, "blocks") and filtered_msg.blocks:
                    filtered_msg.blocks = [
                        block
                        for block in filtered_msg.blocks
                        if not isinstance(block, chat_utils.ImageBlock)
                    ]
                filtered_chat_history.append(filtered_msg)

            # 将聊天历史和响应转换为JSON字符串
            chat_history_str = json.dumps(
                [
                    {"role": msg.role, "content": msg.content}
                    for msg in filtered_chat_history
                ]
            )
            response_str = json.dumps(
                {"role": response.message.role, "content": response.message.content}
            )

            step = EpisodicMemoryStep(
                chat_history=chat_history_str,
                response=response_str,
                timestamp=time.time(),
                screenshot=(await ctx.get("screenshot", None))
            )

            self.episodic_memory.steps.append(step)

            assert hasattr(
                response, "message"
            ), f"LLM响应没有message属性。\n响应: {response}"
        except Exception as e:
            if (
                self.llm.class_name() == "Gemini_LLM"
                and "You exceeded your current quota" in str(e)
            ):
                s = str(e._details[2])
                match = re.search(r"seconds:\s*(\d+)", s)
                if match:
                    seconds = int(match.group(1)) + 1
                    logger.error(f"速率限制错误。将在 {seconds} 秒后重试...")
                    time.sleep(seconds)
                else:
                    logger.error(f"速率限制错误。将在 5 秒后重试...")
                    time.sleep(40)
                logger.debug("🔍 正在重试调用LLM...")
                response = await self.llm.achat(messages=messages_to_send)
            else:
                logger.error(f"无法从LLM获取答案: {repr(e)}")
                raise e
        logger.debug("  - 已从LLM接收响应。")
        return response

    async def _add_final_state_observation(self, ctx: Context) -> None:
        """将当前UI状态和屏幕截图添加为最终观察步骤。"""
        try:
            # 获取当前屏幕截图和UI状态
            screenshot = None
            ui_state = None

            try:
                _, screenshot_bytes = await self.tools.take_screenshot()
                screenshot = screenshot_bytes
            except Exception as e:
                logger.warning(f"捕获最终屏幕截图失败: {e}")

            try:
                (a11y_tree, phone_state) = await self.tools.get_state()
            except Exception as e:
                logger.warning(f"捕获最终UI状态失败: {e}")

            # 创建最终观察聊天历史和响应
            final_chat_history = [{"role": "system", "content": "任务完成后的最终状态观察"}]
            final_response = {
                "role": "user",
                "content": f"最终状态观察:\nUI状态: {a11y_tree}\n屏幕截图: {'可用' if screenshot else '不可用'}"
            }

            # 创建最终情节记忆步骤
            final_step = EpisodicMemoryStep(
                chat_history=json.dumps(final_chat_history),
                response=json.dumps(final_response),
                timestamp=time.time(),
                screenshot=screenshot
            )

            self.episodic_memory.steps.append(final_step)
            logger.info("已将最终状态观察添加到情节记忆")

        except Exception as e:
            logger.error(f"添加最终状态观察失败: {e}")
