from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
import logging
from typing import Tuple, Dict, Callable, Any, Optional

# 为此模块获取一个日志记录器
logger = logging.getLogger(__name__)


class Tools(ABC):
    """
    所有工具的抽象基类。
    此类为所有工具提供了一个通用接口来实现。
    """

    @abstractmethod
    async def get_state(self) -> Dict[str, Any]:
        """
        获取工具的当前状态。
        """
        pass

    @abstractmethod
    async def tap_by_index(self, index: int) -> bool:
        """
        点击给定索引处的元素。
        """
        pass

    #@abstractmethod
    #async def tap_by_coordinates(self, x: int, y: int) -> bool:
    #    pass

    @abstractmethod
    async def swipe(
        self, start_x: int, start_y: int, end_x: int, end_y: int, duration_ms: int = 300
    ) -> bool:
        """
        从给定的起始坐标滑动到给定的结束坐标。
        """
        pass

    @abstractmethod
    async def drag_and_drop(
        self, start_x: int, start_y: int, end_x: int, end_y: int, hold_duration_ms: int = 500
    ) -> bool:
        """
        执行拖拽操作，适用于列表项重排序等需要长按拖动的场景。
        """
        pass

    @abstractmethod
    async def input_text(self, text: str) -> bool:
        """
        将给定的文本输入到聚焦的输入字段中。
        """
        pass

    @abstractmethod
    async def back(self) -> bool:
        """
        按下返回按钮。
        """
        pass

    @abstractmethod
    async def press_key(self, keycode: int) -> bool:
        """
        输入给定的键码。
        """
        pass

    @abstractmethod
    async def start_app(self, package: str, activity: str = "") -> bool:
        """
        启动给定的应用程序。
        """
        pass

    @abstractmethod
    async def take_screenshot(self) -> Tuple[str, bytes]:
        """
        对设备进行截图。
        """
        pass

    @abstractmethod
    async def list_packages(self, include_system_apps: bool = False) -> List[str]:
        """
        列出设备上的所有包。
        """
        pass

    @abstractmethod
    async def remember(self, information: str) -> str:
        """
        记住给定的信息。这用于在工具的内存中存储信息。
        """
        pass

    @abstractmethod
    async def get_memory(self) -> List[str]:
        """
        获取工具的内存。
        """
        pass

    @abstractmethod
    def complete(self, success: bool, reason: str = "") -> bool:
        """
        完成工具。这用于指示工具已完成其任务。
        """
        pass


def describe_tools(tools: Tools) -> Dict[str, Callable[..., Any]]:
    """
    描述给定Tools实例可用的工具。

    参数:
        tools: 要描述的Tools实例。

    返回:
        一个字典，将工具名称映射到它们的描述。
    """

    return {
        # UI交互
        "swipe": tools.swipe,
        "drag_and_drop": tools.drag_and_drop,
        "input_text": tools.input_text,
        "press_key": tools.press_key,
        "tap_by_index": tools.tap_by_index,
        # 应用管理
        "start_app": tools.start_app,
        "list_packages": tools.list_packages,
        # 状态管理
        "remember": tools.remember,
        "complete": tools.complete,
    }
