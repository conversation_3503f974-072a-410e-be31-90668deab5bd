# DroidRun中文AI代理配置指南

## 概述

DroidRun现在支持中文AI代理，让AI在思考过程和回复中都使用中文。本指南将详细介绍如何配置和使用中文AI代理。

## 🚀 快速开始

### 1. 基本配置

```python
from droidrun import AdbTools, DroidAgent
from droidrun.config.language_config import set_language, Language
from llama_index.llms.openai import OpenAI

# 设置语言为中文（在创建代理之前）
set_language(Language.CHINESE)

# 创建代理
agent = DroidAgent(
    goal="打开设置应用并检查Android版本",
    llm=OpenAI(model="gpt-4o", api_key="your-api-key"),
    tools=AdbTools(),
    reasoning=True,
    debug=True
)

# 执行任务
result = await agent.run()
```

### 2. 语言切换

```python
from droidrun.config.language_config import set_language, Language

# 切换到中文
set_language(Language.CHINESE)

# 切换到英文
set_language(Language.ENGLISH)

# 检查当前语言
from droidrun.config.language_config import get_language, is_chinese
current_lang = get_language()
if is_chinese():
    print("当前使用中文模式")
```

## 📁 修改的文件

为了支持中文AI代理，以下文件已被修改：

### 1. Persona配置文件
- `droidrun/agent/context/personas/default.py` - 默认代理中文化
- `droidrun/agent/context/personas/ui_expert.py` - UI专家代理中文化  
- `droidrun/agent/context/personas/app_starter.py` - 应用启动专家中文化

### 2. 提示词文件
- `droidrun/agent/planner/prompts.py` - 规划器提示词中文化
- `droidrun/agent/codeact/prompts.py` - 代码执行代理提示词中文化

### 3. 新增配置文件
- `droidrun/config/language_config.py` - 语言配置管理模块

## 🔧 详细配置说明

### 语言配置模块

`language_config.py` 提供了以下功能：

```python
# 设置语言
set_language(Language.CHINESE)  # 中文
set_language(Language.ENGLISH)  # 英文

# 检查当前语言
is_chinese()  # 返回True/False
is_english()  # 返回True/False
get_language()  # 返回Language枚举

# 获取本地化文本
get_prompt_text("current_request")  # 根据当前语言返回对应文本
```

### AI代理行为变化

启用中文模式后，AI代理将：

1. **思考过程中文化**：代理的分析和推理过程将使用中文
2. **操作说明中文化**：代理执行操作时的说明使用中文
3. **错误信息中文化**：失败原因和错误信息使用中文
4. **完成状态中文化**：任务完成的反馈使用中文

### 示例输出对比

**英文模式：**
```
**(Step 1) Agent Analysis:** I can see the Settings app is open from the screenshot...
**(Step 1) Agent Action:**
```python
# First step: Navigate to Wi-Fi settings
tap_by_index(3)
```

**中文模式：**
```
**（步骤1）代理分析：** 我可以从截图中看到设置应用已经打开...
**（步骤1）代理操作：**
```python
# 第一步：导航到Wi-Fi设置
tap_by_index(3)
```

## 📝 使用示例

### 完整示例

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from droidrun.config.language_config import set_language, Language
from llama_index.llms.openai import OpenAI

async def main():
    # 设置中文模式
    set_language(Language.CHINESE)
    
    # 初始化组件
    llm = OpenAI(model="gpt-4o", api_key="your-api-key")
    tools = AdbTools()
    
    # 创建中文代理
    agent = DroidAgent(
        goal="打开微信，查看最新消息",
        llm=llm,
        tools=tools,
        vision=True,
        reasoning=True,
        debug=True
    )
    
    # 执行任务
    result = await agent.run()
    print(f"任务结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

### CLI使用

```bash
# 设置环境变量（如果需要）
export DROIDRUN_LANGUAGE=zh

# 使用中文命令
droidrun "打开设置应用并告诉我安卓版本" --provider OpenAI --model gpt-4o
```

## ⚠️ 注意事项

1. **设置时机**：必须在创建`DroidAgent`之前调用`set_language()`
2. **全局设置**：语言设置是全局的，影响所有后续创建的代理
3. **重启保持**：语言设置在程序重启后会重置为默认值
4. **模型兼容性**：确保使用的LLM模型支持中文（如GPT-4、Claude等）

## 🔄 恢复英文模式

如果需要恢复到英文模式：

```python
from droidrun.config.language_config import set_language, Language

# 恢复英文模式
set_language(Language.ENGLISH)
```

## 🛠️ 自定义扩展

如果需要添加更多语言或自定义提示词：

1. 在`Language`枚举中添加新语言
2. 在`LANGUAGE_PROMPTS`字典中添加对应的提示词
3. 更新相关的persona和prompt文件

## 📞 技术支持

如果在使用中文AI代理时遇到问题：

1. 检查语言设置是否在代理创建前调用
2. 确认LLM模型支持中文
3. 查看debug日志中的详细信息
4. 参考`examples/chinese_agent_example.py`中的完整示例

---

**享受使用中文AI代理的便利！** 🎉
