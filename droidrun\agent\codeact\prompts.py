"""
Prompt templates for the CodeActAgent.

This module contains all the prompts used by the CodeActAgent,
separated from the workflow logic for better maintainability.
"""


# User prompt template that presents the current request and prompts for reasoning
DEFAULT_CODE_ACT_USER_PROMPT = """**当前请求：**
{goal}

**前置条件是否满足？你的推理过程是什么，下一步要采取什么行动来处理这个请求？** 请解释你的思考过程，然后在需要时提供 ```python ... ``` 标签中的代码。"""""

# Prompt to remind the agent to provide thoughts before code
DEFAULT_NO_THOUGHTS_PROMPT = """你之前的响应在没有首先解释推理的情况下提供了代码。记住要始终在提供代码块*之前*描述你的思考过程和计划。

你提供的代码将在下面执行。

现在，描述你将采取的下一步来解决原始目标：{goal}"""

# Export all prompts
__all__ = [
    "DEFAULT_CODE_ACT_USER_PROMPT", 
    "DEFAULT_NO_THOUGHTS_PROMPT"
] 