import io
import contextlib
import ast
import traceback
import logging
from typing import Any, Dict
from droidrun.agent.utils.async_utils import async_to_sync
from llama_index.core.workflow import Context
import asyncio
from asyncio import AbstractEventLoop
import threading

logger = logging.getLogger("droidrun")


class SimpleCodeExecutor:
    """
    一个简单的代码执行器，运行具有状态持久性的Python代码。

    此执行器在执行之间维护全局和局部状态，
    允许变量在多次代码运行中持久存在。

    注意：不适用于生产环境！请谨慎使用。
    """

    def __init__(
        self,
        loop: AbstractEventLoop,
        locals: Dict[str, Any] = {},
        globals: Dict[str, Any] = {},
        tools={},
        use_same_scope: bool = True,
    ):
        """
        初始化代码执行器。

        Args:
            locals: 在执行上下文中使用的局部变量
            globals: 在执行上下文中使用的全局变量
            tools: 可用于执行的工具列表
        """

        # 遍历工具并将它们添加到全局变量中，但在此之前检查工具值是否为异步，如果是则转换为同步。tools是工具名称:函数的字典
        # 例如 tools = {'tool_name': tool_function}

        # 检查tools是否为字典
        if isinstance(tools, dict):
            logger.debug(
                f"🔧 正在使用工具初始化SimpleCodeExecutor: {tools.items()}"
            )
            for tool_name, tool_function in tools.items():
                if asyncio.iscoroutinefunction(tool_function):
                    # 如果函数是异步的，将其转换为同步
                    tool_function = async_to_sync(tool_function)
                # 将工具添加到全局变量
                globals[tool_name] = tool_function
        elif isinstance(tools, list):
            logger.debug(f"🔧 正在使用工具初始化SimpleCodeExecutor: {tools}")
            # 如果tools是列表，将其转换为以工具名称为键、函数为值的字典
            for tool in tools:
                if asyncio.iscoroutinefunction(tool):
                    # 如果函数是异步的，将其转换为同步
                    tool = async_to_sync(tool)
                # 将工具添加到全局变量
                globals[tool.__name__] = tool
        else:
            raise ValueError("工具必须是字典或函数列表。")

        import time

        globals["time"] = time

        self.globals = globals
        self.locals = locals
        self.loop = loop
        self.use_same_scope = use_same_scope
        if self.use_same_scope:
            # 如果使用相同作用域，将全局变量和局部变量设置为同一个字典
            self.globals = self.locals = {
                **self.locals,
                **{k: v for k, v in self.globals.items() if k not in self.locals},
            }

    async def execute(self, ctx: Context, code: str) -> str:
        """
        执行Python代码并捕获输出和返回值。

        Args:
            code: 要执行的Python代码

        Returns:
            str: 执行的输出，包括print语句。
        """
        # 执行前更新UI元素
        self.globals['ui_state'] = await ctx.get("ui_state", None)

        # 捕获stdout和stderr
        stdout = io.StringIO()
        stderr = io.StringIO()

        output = ""
        try:
            # 执行并捕获输出
            thread_exception = []
            with contextlib.redirect_stdout(stdout), contextlib.redirect_stderr(stderr):

                def execute_code():
                    try:
                        exec(code, self.globals, self.locals)
                    except Exception as e:
                        import traceback

                        thread_exception.append((e, traceback.format_exc()))

                t = threading.Thread(target=execute_code)
                t.start()
                t.join()

            # 获取输出
            output = stdout.getvalue()
            if stderr.getvalue():
                output += "\n" + stderr.getvalue()
            if thread_exception:
                e, tb = thread_exception[0]
                output += f"\n错误: {type(e).__name__}: {str(e)}\n{tb}"

        except Exception as e:
            # 捕获异常信息
            output = f"错误: {type(e).__name__}: {str(e)}\n"
            output += traceback.format_exc()

        return output
