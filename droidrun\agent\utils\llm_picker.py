import importlib
import logging
from typing import Any
from llama_index.core.llms.llm import LLM
# 配置日志记录
logger = logging.getLogger("droidrun")

def load_llm(provider_name: str, **kwargs: Any) -> LLM:
    """
    动态加载并初始化LlamaIndex LLM。

    导入 `llama_index.llms.<provider_name_lower>`，在该模块中找到名为
    `provider_name` 的类，验证它是LLM子类，
    并使用kwargs初始化它。

    Args:
        provider_name: 提供商和类的区分大小写名称
                       (例如 "OpenAI", "Ollama", "HuggingFaceLLM")。
        **kwargs: LLM类构造函数的关键字参数。

    Returns:
        初始化的LLM实例。

    Raises:
        ModuleNotFoundError: 如果找不到提供商的模块。
        AttributeError: 如果在模块中找不到类 `provider_name`。
        TypeError: 如果找到的类不是LLM的子类或kwargs无效。
        RuntimeError: 其他初始化错误。
    """
    if not provider_name:
        raise ValueError("provider_name不能为空。")
    if provider_name == "OpenAILike":
        module_provider_part = "openai_like"
        kwargs.setdefault("is_chat_model", True)
    elif provider_name == "GoogleGenAI":
        module_provider_part = "google_genai"
    else:
        # 使用小写作为模块路径，处理包名建议的连字符
        lower_provider_name = provider_name.lower()
        # 特殊情况常见变体，如 HuggingFaceLLM -> huggingface 模块
        if lower_provider_name.endswith("llm"):
            module_provider_part = lower_provider_name[:-3].replace("-", "_")
        else:
            module_provider_part = lower_provider_name.replace("-", "_")
    module_path = f"llama_index.llms.{module_provider_part}"
    install_package_name = f"llama-index-llms-{module_provider_part.replace('_', '-')}"

    try:
        logger.debug(f"尝试导入模块: {module_path}")
        llm_module = importlib.import_module(module_path)
        logger.debug(f"成功导入模块: {module_path}")

    except ModuleNotFoundError:
        logger.error(f"未找到模块 '{module_path}'。请尝试: pip install {install_package_name}")
        raise ModuleNotFoundError(
            f"无法导入 '{module_path}'。是否已安装 '{install_package_name}'？"
        ) from None

    try:
        logger.debug(f"尝试从模块 {module_path} 获取类 '{provider_name}'")
        llm_class = getattr(llm_module, provider_name)
        logger.debug(f"找到类: {llm_class.__name__}")

        # 验证类是LLM的子类
        if not isinstance(llm_class, type) or not issubclass(llm_class, LLM):
            raise TypeError(f"在 '{module_path}' 中找到的类 '{provider_name}' 不是有效的LLM子类。")

        # 从kwargs中过滤掉None值
        filtered_kwargs = {k: v for k, v in kwargs.items() if v is not None}

        # 初始化
        logger.debug(f"正在使用kwargs初始化 {llm_class.__name__}: {list(filtered_kwargs.keys())}")
        llm_instance = llm_class(**filtered_kwargs)
        logger.debug(f"成功加载并初始化LLM: {provider_name}")
        if not llm_instance:
            raise RuntimeError(f"为 {provider_name} 初始化LLM实例失败。")
        return llm_instance

    except AttributeError:
        logger.error(f"在模块 '{module_path}' 中未找到类 '{provider_name}'。")
        raise AttributeError(
            f"在模块 '{module_path}' 中找不到类 '{provider_name}'。请检查拼写和大小写。"
        ) from None
    except TypeError as e:
        logger.error(f"初始化 {provider_name} 时出错: {e}")
        raise # 重新抛出TypeError（可能来自issubclass检查或__init__）
    except Exception as e:
        logger.error(f"初始化 {provider_name} 时发生意外错误: {e}")
        raise e
    
# --- 使用示例 ---
if __name__ == "__main__":
    # 安装您想要测试的特定LLM集成:
    # pip install \
    #   llama-index-llms-anthropic \
    #   llama-index-llms-deepseek \
    #   llama-index-llms-gemini \
    #   llama-index-llms-openai

    # 示例 1: 加载Anthropic (需要ANTHROPIC_API_KEY环境变量或kwarg)
    print("\n--- Loading Anthropic ---")
    try:
        anthropic_llm = load_llm(
            "Anthropic",
            model="claude-3-7-sonnet-latest",
        )
        print(f"Loaded LLM: {type(anthropic_llm)}")
        print(f"Model: {anthropic_llm.metadata}")
    except Exception as e:
        print(f"Failed to load Anthropic: {e}")

    # 示例 2: 加载DeepSeek (需要DEEPSEEK_API_KEY环境变量或kwarg)
    print("\n--- 加载DeepSeek ---")
    try:
        deepseek_llm = load_llm(
            "DeepSeek",
            model="deepseek-reasoner",
            api_key="your api",  # 或设置DEEPSEEK_API_KEY
        )
        print(f"已加载LLM: {type(deepseek_llm)}")
        print(f"模型: {deepseek_llm.metadata}")
    except Exception as e:
        print(f"加载DeepSeek失败: {e}")

    # 示例 3: 加载Gemini (需要GOOGLE_APPLICATION_CREDENTIALS或kwarg)
    print("\n--- 加载Gemini ---")
    try:
        gemini_llm = load_llm(
            "Gemini",
            model="gemini-2.0-fash",
        )
        print(f"已加载LLM: {type(gemini_llm)}")
        print(f"模型: {gemini_llm.metadata}")
    except Exception as e:
        print(f"加载Gemini失败: {e}")

    # 示例 4: 加载OpenAI (需要OPENAI_API_KEY环境变量或kwarg)
    print("\n--- 加载OpenAI ---")
    try:
        openai_llm = load_llm(
            "OpenAI",
            model="gp-4o",
            temperature=0.5,
        )
        print(f"已加载LLM: {type(openai_llm)}")
        print(f"模型: {openai_llm.metadata}")
    except Exception as e:
        print(f"加载OpenAI失败: {e}")
