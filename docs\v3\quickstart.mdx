---
title: '快速开始'
description: '快速有效地启动并运行DroidRun'
---

<iframe
  className="w-full aspect-video rounded-xl"
  src="https://www.youtube.com/embed/4WT7FXJah2I"
  title="YouTube video player"
  frameBorder="0"
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
  allowFullScreen
></iframe>

本指南将帮助您快速安装和运行DroidRun，在几分钟内通过自然语言控制您的Android设备。

### 前提条件

在安装DroidRun之前，请确保您具备：

1. 系统上安装了**Python 3.10+**
2. 已安装并配置[Android Debug Bridge (adb)](https://developer.android.com/studio/releases/platform-tools)
3. **Android设备**具备：
   - [已启用开发者选项](https://developer.android.com/studio/debug/dev-options)
   - 已启用USB调试
   - 通过USB连接或在同一网络上（用于无线调试）

### 从PyPI安装
```bash
pip install droidrun
```

### 设置Portal APK
```bash
droidrun setup
```

### 测试功能
```bash
droidrun ping
```

### 通过CLI运行您的第一个命令

```bash
export OPENAI_API_KEY=<YOUR API KEY>
droidrun "打开设置应用并告诉我Android版本" --provider OpenAI --model gpt-4o
```

### 通过脚本创建简单代理

对于复杂的自动化，创建一个Python脚本：

```python
#!/usr/bin/env python3
import asyncio
from droidrun import AdbTools, DroidAgent
from llama_index.llms.google_genai import GoogleGenAI

async def main():
    # 加载工具
    tools = await AdbTools.create()
    # 设置Google Gemini LLM
    llm = GoogleGenAI(
        api_key="YOUR_GEMINI_API_KEY",  # 替换为您的Gemini API密钥
        model="gemini-2.5-flash",  # 或使用"gemini-2.5-pro"以增强推理能力
    )

    # 创建代理
    agent = DroidAgent(
        goal="打开设置并检查电池电量",
        llm=llm,
        tools=tools
    )

    # 运行代理
    result = await agent.run()
    print(f"成功: {result['success']}")
    if result.get('output'):
        print(f"输出: {result['output']}")

if __name__ == "__main__":
    asyncio.run(main())
```


## 下一步

现在您已经运行了DroidRun，您可以：

- 了解[代理](/v3/concepts/agent)
- 查看支持的[LLM提供商](/v3/concepts/models)
- 探索[Android交互](/v3/concepts/android-control)
- 了解[Portal应用](/v3/concepts/portal-app)