import base64
import re
import inspect


import json
import logging
from typing import List, TYPE_CHECKING, Optional, Tuple
from droidrun.agent.context import Reflection
from llama_index.core.base.llms.types import ChatMessage, ImageBlock, TextBlock

if TYPE_CHECKING:
    from droidrun.tools import Tools

logger = logging.getLogger("droidrun")

def message_copy(message: ChatMessage, deep = True) -> ChatMessage:
    if deep:
        copied_message = message.model_copy()
        copied_message.blocks = [block.model_copy () for block in message.blocks]

        return copied_message
    copied_message = message.model_copy()

    # 创建一个新的独立列表，包含相同的块引用
    copied_message.blocks = list(message.blocks) # 或 original_message.blocks[:]

    return copied_message

async def add_reflection_summary(reflection: Reflection, chat_history: List[ChatMessage]) -> List[ChatMessage]:
    """添加反思摘要和建议，帮助规划器理解出了什么问题以及如何改进。"""

    reflection_text = "\n### 上一个任务失败了。您有关于发生了什么的额外信息。\n来自上次尝试的反思：\n"

    if reflection.summary:
        reflection_text += f"**发生了什么：** {reflection.summary}\n\n"

    if reflection.advice:
        reflection_text += f"**此次重试的推荐方法：** {reflection.advice}\n"
    
    reflection_block = TextBlock(text=reflection_text)
    
    # 复制聊天历史并将反思块附加到最后一条消息
    chat_history = chat_history.copy()
    chat_history[-1] = message_copy(chat_history[-1])
    chat_history[-1].blocks.append(reflection_block)
    
    return chat_history

def _format_ui_elements(ui_data, level=0) -> str:
    """以自然语言格式化UI元素：index. className: resourceId, text - bounds"""
    if not ui_data:
        return ""

    formatted_lines = []
    indent = "  " * level  # 嵌套元素的缩进

    # 处理列表和单个元素
    elements = ui_data if isinstance(ui_data, list) else [ui_data]
    
    for element in elements:
        if not isinstance(element, dict):
            continue
            
        # 提取元素属性
        index = element.get('index', '')
        class_name = element.get('className', '')
        resource_id = element.get('resourceId', '')
        text = element.get('text', '')
        bounds = element.get('bounds', '')
        children = element.get('children', [])


        # 格式化行：index. className: resourceId, text - bounds
        line_parts = []
        if index != '':
            line_parts.append(f"{index}.")
        if class_name:
            line_parts.append(class_name + ":")
        
        details = []
        if resource_id:
            details.append(f'"{resource_id}"')
        if text:
            details.append(f'"{text}"')
        if details:
            line_parts.append(", ".join(details))
        
        if bounds:
            line_parts.append(f"- ({bounds})")
        
        formatted_line = f"{indent}{' '.join(line_parts)}"
        formatted_lines.append(formatted_line)
        
        # 递归格式化子元素，增加缩进
        if children:
            child_formatted = _format_ui_elements(children, level + 1)
            if child_formatted:
                formatted_lines.append(child_formatted)
    
    return "\n".join(formatted_lines)

async def add_ui_text_block(ui_state: str, chat_history: List[ChatMessage], copy = True) -> List[ChatMessage]:
    """将UI元素添加到聊天历史中而不修改原始内容。"""
    if ui_state:
        # 解析JSON并以自然语言格式化
        try:
            ui_data = json.loads(ui_state) if isinstance(ui_state, str) else ui_state
            formatted_ui = _format_ui_elements(ui_data)
            ui_block = TextBlock(text=f"\n设备当前可点击UI元素，格式为'index. className: resourceId, text - bounds(x1,y1,x2,y2)':\n{formatted_ui}\n")
        except (json.JSONDecodeError, TypeError):
            # 如果解析失败则回退到原始格式
            ui_block = TextBlock(text="\n使用自定义TopViewService的设备当前可点击UI元素:\n```json\n" + json.dumps(ui_state) + "\n```\n")

        if copy:
            chat_history = chat_history.copy()
            chat_history[-1] = message_copy(chat_history[-1])
        chat_history[-1].blocks.append(ui_block)
    return chat_history

async def add_screenshot_image_block(screenshot, chat_history: List[ChatMessage], copy = True) -> None:
    if screenshot:
        image_block = ImageBlock(image=base64.b64encode(screenshot))
        if copy:
            chat_history = chat_history.copy()  # 创建聊天历史的副本以避免修改原始内容
            chat_history[-1] = message_copy(chat_history[-1])
        chat_history[-1].blocks.append(image_block)
    return chat_history


async def add_phone_state_block(phone_state, chat_history: List[ChatMessage]) -> List[ChatMessage]:
    
    # 格式化手机状态数据
    if isinstance(phone_state, dict) and 'error' not in phone_state:
        current_app = phone_state.get('currentApp', '')
        package_name = phone_state.get('packageName', '未知')
        keyboard_visible = phone_state.get('keyboardVisible', False)
        focused_element = phone_state.get('focusedElement')

        # 格式化焦点元素
        if focused_element:
            element_text = focused_element.get('text', '')
            element_class = focused_element.get('className', '')
            element_resource_id = focused_element.get('resourceId', '')

            # 构建焦点元素描述
            focused_desc = f"'{element_text}' {element_class}"
            if element_resource_id:
                focused_desc += f" | ID: {element_resource_id}"
        else:
            focused_desc = "无"

        phone_state_text = f"""
**当前手机状态:**
• **应用:** {current_app} ({package_name})
• **键盘:** {'可见' if keyboard_visible else '隐藏'}
• **焦点元素:** {focused_desc}
        """
    else:
        # 处理错误情况或格式错误的数据
        if isinstance(phone_state, dict) and 'error' in phone_state:
            phone_state_text = f"\n📱 **手机状态错误:** {phone_state.get('message', '未知错误')}\n"
        else:
            phone_state_text = f"\n📱 **手机状态:** {phone_state}\n"
    
    ui_block = TextBlock(text=phone_state_text)
    chat_history = chat_history.copy()
    chat_history[-1] = message_copy(chat_history[-1])
    chat_history[-1].blocks.append(ui_block)
    return chat_history

async def add_packages_block(packages, chat_history: List[ChatMessage]) -> List[ChatMessage]:
    
    ui_block = TextBlock(text=f"\n已安装的包: {packages}\n```\n")
    chat_history = chat_history.copy()
    chat_history[-1] = message_copy(chat_history[-1])
    chat_history[-1].blocks.append(ui_block)
    return chat_history

async def add_memory_block(memory: List[str], chat_history: List[ChatMessage]) -> List[ChatMessage]:
    memory_block = "\n### 记忆信息:\n"
    for idx, item in enumerate(memory, 1):
        memory_block += f"{idx}. {item}\n"
    
    for i, msg in enumerate(chat_history):
        if msg.role == "user":
            if isinstance(msg.content, str):
                updated_content = f"{memory_block}\n\n{msg.content}"
                chat_history[i] = ChatMessage(role="user", content=updated_content)
            elif isinstance(msg.content, list):
                memory_text_block = TextBlock(text=memory_block)
                content_blocks = [memory_text_block] + msg.content
                chat_history[i] = ChatMessage(role="user", content=content_blocks)
            break
    return chat_history

async def get_reflection_block(reflections: List[Reflection]) -> ChatMessage:
    reflection_block = "\n### 您还有来自以往经验的额外知识来帮助指导当前任务:\n"
    for reflection in reflections:
        reflection_block += f"**{reflection.advice}\n"
    
    return ChatMessage(role="user", content=reflection_block)
        
async def add_task_history_block(completed_tasks: list[dict], failed_tasks: list[dict], chat_history: List[ChatMessage]) -> List[ChatMessage]:
    task_history = ""

    # 合并所有任务并按时间顺序显示
    all_tasks = completed_tasks + failed_tasks

    if all_tasks:
        task_history += "任务历史（按时间顺序）:\n"
        for i, task in enumerate(all_tasks, 1):
            if hasattr(task, 'description'):
                status_indicator = "[成功]" if hasattr(task, 'status') and task.status == "completed" else "[失败]"
                task_history += f"{i}. {status_indicator} {task.description}\n"
            elif isinstance(task, dict):
                # 为了与字典格式的向后兼容性
                task_description = task.get('description', str(task))
                status_indicator = "[成功]" if task in completed_tasks else "[失败]"
                task_history += f"{i}. {status_indicator} {task_description}\n"
            else:
                status_indicator = "[成功]" if task in completed_tasks else "[失败]"
                task_history += f"{i}. {status_indicator} {task}\n"

    
    task_block = TextBlock(text=f"{task_history}")

    chat_history = chat_history.copy()
    chat_history[-1] = message_copy(chat_history[-1])
    chat_history[-1].blocks.append(task_block)
    return chat_history

def parse_tool_descriptions(tool_list) -> str:
    """解析可用工具及其描述以用于系统提示。"""
    logger.info("🛠️  正在解析工具描述...")
    tool_descriptions = []

    for tool in tool_list.values():
        assert callable(tool), f"工具 {tool} 不可调用。"
        tool_name = tool.__name__
        tool_signature = inspect.signature(tool)
        tool_docstring = tool.__doc__ or "无可用描述。"
        formatted_signature = f"def {tool_name}{tool_signature}:\n    \"\"\"{tool_docstring}\"\"\"\n..."
        tool_descriptions.append(formatted_signature)
        logger.debug(f"  - 已解析工具: {tool_name}")
    descriptions = "\n".join(tool_descriptions)
    logger.info(f"🔩 找到 {len(tool_descriptions)} 个工具。")
    return descriptions


def parse_persona_description(personas) -> str:
    """解析可用的代理角色及其描述以用于系统提示。"""
    logger.debug("👥 正在为规划代理解析代理角色描述...")

    if not personas:
        logger.warning("未向规划代理提供代理角色")
        return "无可用的专业代理。"

    persona_descriptions = []
    for persona in personas:
        # 使用名称、描述和专业领域格式化每个角色
        expertise_list = ", ".join(persona.expertise_areas) if persona.expertise_areas else "通用任务"
        formatted_persona = f"- **{persona.name}**: {persona.description}\n  专业领域: {expertise_list}"
        persona_descriptions.append(formatted_persona)
        logger.debug(f"  - 已解析角色: {persona.name}")

    # 将所有角色描述连接成单个字符串
    descriptions = "\n".join(persona_descriptions)
    logger.debug(f"👤 找到 {len(persona_descriptions)} 个代理角色。")
    return descriptions


def extract_code_and_thought(response_text: str) -> Tuple[Optional[str], str]:
    """
    从Markdown块（```python ... ```）中提取代码和周围的文本（思考），
    处理缩进的代码块。

    Returns:
        Tuple[Optional[code_string], thought_string]
    """
    logger.debug("✂️ 正在从响应中提取代码和思考...")
    code_pattern = r"^\s*```python\s*\n(.*?)\n^\s*```\s*?$"
    code_matches = list(re.finditer(code_pattern, response_text, re.DOTALL | re.MULTILINE))

    if not code_matches:
        logger.debug("  - 未找到代码块。整个响应都是思考。")
        return None, response_text.strip()

    extracted_code_parts = []
    for match in code_matches:
            code_content = match.group(1)
            extracted_code_parts.append(code_content)

    extracted_code = "\n\n".join(extracted_code_parts)


    thought_parts = []
    last_end = 0
    for match in code_matches:
        start, end = match.span(0)
        thought_parts.append(response_text[last_end:start])
        last_end = end
    thought_parts.append(response_text[last_end:])

    thought_text = "".join(thought_parts).strip()
    thought_preview = (thought_text[:100] + '...') if len(thought_text) > 100 else thought_text
    logger.debug(f"  - 提取的思考: {thought_preview}")

    return extracted_code, thought_text