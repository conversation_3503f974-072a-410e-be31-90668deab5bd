import logging
from rich.layout import Layout
from rich.panel import Panel
from rich.spinner import Spinner
from rich.console import Console
from rich.live import Live
from typing import List

from droidrun.agent.common.events import ScreenshotEvent
from droidrun.agent.planner.events import (
    PlanInputEvent,
    PlanThinkingEvent,
    PlanCreatedEvent,
)
from droidrun.agent.codeact.events import (
    TaskInputEvent,
    TaskThinkingEvent,
    TaskExecutionEvent,
    TaskExecutionResultEvent,
    TaskEndEvent,
)
from droidrun.agent.droid.events import (
    CodeActExecuteEvent,
    CodeActResultEvent,
    ReasoningLogicEvent,
    TaskRunnerEvent,
    FinalizeEvent,
)


class LogHandler(logging.Handler):
    def __init__(self, goal: str, current_step: str = "正在初始化..."):
        super().__init__()

        self.goal = goal
        self.current_step = current_step
        self.is_completed = False
        self.is_success = False
        self.spinner = Spinner("dots")
        self.console = Console()
        self.layout = self._create_layout()
        self.logs: List[str] = []

    def emit(self, record):
        msg = self.format(record)
        lines = msg.splitlines()

        for line in lines:
            self.logs.append(line)
            # 可选择性地限制日志列表大小
            if len(self.logs) > 100:
                self.logs.pop(0)

        self.rerender()

    def render(self):
        return Live(self.layout, refresh_per_second=4, console=self.console)

    def rerender(self):
        self._update_layout(
            self.layout,
            self.logs,
            self.current_step,
            self.goal,
            self.is_completed,
            self.is_success,
        )

    def update_step(self, step: str):
        self.current_step = step
        self.rerender()

    def _create_layout(self):
        """创建一个布局，顶部显示日志，底部显示状态"""
        layout = Layout()
        layout.split(
            Layout(name="logs"),
            Layout(name="goal", size=3),
            Layout(name="status", size=3),
        )
        return layout

    def _update_layout(
        self,
        layout: Layout,
        log_list: List[str],
        step_message: str,
        goal: str = None,
        completed: bool = False,
        success: bool = False,
    ):
        """使用当前日志和步骤信息更新布局"""
        from rich.text import Text
        import shutil

        # 缓存终端大小以避免频繁重新计算
        try:
            terminal_height = shutil.get_terminal_size().lines
        except:
            terminal_height = 24  # 回退值

        # 为面板和边框预留空间（更保守的估计）
        other_components_height = 10  # 目标面板 + 状态面板 + 边框 + 填充
        available_log_lines = max(8, terminal_height - other_components_height)

        # 只显示最近的日志，但确保不会闪烁
        visible_logs = (
            log_list[-available_log_lines:]
            if len(log_list) > available_log_lines
            else log_list
        )

        # 确保始终有一些内容以防止面板折叠
        if not visible_logs:
            visible_logs = ["正在初始化..."]

        log_content = "\n".join(visible_logs)

        layout["logs"].update(
            Panel(
                log_content,
                title=f"活动日志 ({len(log_list)} 条记录)",
                border_style="blue",
                title_align="left",
                padding=(0, 1),
                height=available_log_lines + 2,
            )
        )

        if goal:
            goal_text = Text(goal, style="bold")
            layout["goal"].update(
                Panel(
                    goal_text,
                    title="目标",
                    border_style="magenta",
                    title_align="left",
                    padding=(0, 1),
                    height=3,
                )
            )

        step_display = Text()

        if completed:
            if success:
                step_display.append("✓ ", style="bold green")
                panel_title = "已完成"
                panel_style = "green"
            else:
                step_display.append("✗ ", style="bold red")
                panel_title = "失败"
                panel_style = "red"
        else:
            step_display.append("⚡ ", style="bold yellow")
            panel_title = "状态"
            panel_style = "yellow"

        step_display.append(step_message)

        layout["status"].update(
            Panel(
                step_display,
                title=panel_title,
                border_style=panel_style,
                title_align="left",
                padding=(0, 1),
                height=3,
            )
        )

    def handle_event(self, event):
        """处理来自代理工作流的流式事件。"""
        logger = logging.getLogger("droidrun")

        # 使用适当的名称记录不同的事件类型
        if isinstance(event, ScreenshotEvent):
            logger.debug("📸 正在截取屏幕...")

        # 规划器事件
        elif isinstance(event, PlanInputEvent):
            self.current_step = "正在规划..."
            logger.info("💭 规划器正在接收输入...")

        elif isinstance(event, PlanThinkingEvent):
            if event.thoughts:
                thoughts_preview = (
                    event.thoughts[:150] + "..."
                    if len(event.thoughts) > 150
                    else event.thoughts
                )
                logger.info(f"🧠 规划中: {thoughts_preview}")
            if event.code:
                logger.info(f"📝 已生成规划代码")

        elif isinstance(event, PlanCreatedEvent):
            if event.tasks:
                task_count = len(event.tasks) if event.tasks else 0
                self.current_step = f"规划就绪 ({task_count} 个任务)"
                logger.info(f"📋 已创建包含 {task_count} 个任务的规划")
                for task in event.tasks:
                    desc = task.description
                    logger.info(f"- {desc}")

        # CodeAct事件
        elif isinstance(event, TaskInputEvent):
            self.current_step = "正在处理任务输入..."
            logger.info("💬 已接收任务输入...")

        elif isinstance(event, TaskThinkingEvent):
            if hasattr(event, "thoughts") and event.thoughts:
                thoughts_preview = (
                    event.thoughts[:150] + "..."
                    if len(event.thoughts) > 150
                    else event.thoughts
                )
                logger.info(f"🧠 思考中: {thoughts_preview}")
            if hasattr(event, "code") and event.code:
                logger.info(f"💻 正在执行动作代码")
                logger.debug(f"{event.code}")

        elif isinstance(event, TaskExecutionEvent):
            self.current_step = "正在执行动作..."
            logger.info(f"⚡ 正在执行动作...")

        elif isinstance(event, TaskExecutionResultEvent):
            if hasattr(event, "output") and event.output:
                output = str(event.output)
                if "Error" in output or "Exception" in output:
                    output_preview = (
                        output[:100] + "..." if len(output) > 100 else output
                    )
                    logger.info(f"❌ 动作错误: {output_preview}")
                else:
                    output_preview = (
                        output[:100] + "..." if len(output) > 100 else output
                    )
                    logger.info(f"⚡ 动作结果: {output_preview}")

        elif isinstance(event, TaskEndEvent):
            if hasattr(event, "success") and hasattr(event, "reason"):
                if event.success:
                    self.current_step = event.reason
                    logger.info(f"✅ 任务完成: {event.reason}")
                else:
                    self.current_step = f"任务失败"
                    logger.info(f"❌ 任务失败: {event.reason}")

        # Droid协调事件
        elif isinstance(event, CodeActExecuteEvent):
            self.current_step = "正在执行任务..."
            logger.info(f"🔧 开始执行任务...")

        elif isinstance(event, CodeActResultEvent):
            if hasattr(event, "success") and hasattr(event, "reason"):
                if event.success:
                    self.current_step = event.reason
                    logger.info(f"✅ 任务完成: {event.reason}")
                else:
                    self.current_step = f"任务失败"
                    logger.info(f"❌ 任务失败: {event.reason}")

        elif isinstance(event, ReasoningLogicEvent):
            self.current_step = "正在规划..."
            logger.info(f"🤔 正在规划下一步...")

        elif isinstance(event, TaskRunnerEvent):
            self.current_step = "正在处理任务..."
            logger.info(f"🏃 正在处理任务队列...")

        elif isinstance(event, FinalizeEvent):
            if hasattr(event, "success") and hasattr(event, "reason"):
                self.is_completed = True
                self.is_success = event.success
                if event.success:
                    self.current_step = f"成功: {event.reason}"
                    logger.info(f"🎉 目标达成: {event.reason}")
                else:
                    self.current_step = f"失败: {event.reason}"
                    logger.info(f"❌ 目标失败: {event.reason}")

        else:
            logger.debug(f"🔄 {event.__class__.__name__}")
