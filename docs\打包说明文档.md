# DroidRun 打包说明文档

## 📋 打包脚本

### `build_wheel.py` ⭐ **本地打包专用脚本**
- **用途**: 本地打包，生成标准 Python 包
- **特点**: 简单可靠，避免环境问题，支持配置切换
- **生成**: `.whl` 和 `.tar.gz` 文件
- **目标**: 创建 `.dist-info` 目录结构
- **配置管理**: 自动切换开发配置和打包配置

## 🔧 配置文件管理

### 双配置系统
- **`pyproject.toml`** - 开发配置（日常开发使用）
- **`pyproject_build.toml`** - 打包配置（打包时使用）

### 自动切换机制
打包脚本会自动：
1. 备份当前 `pyproject.toml` 为 `pyproject_back.toml`
2. 将 `pyproject_build.toml` 复制为 `pyproject.toml`
3. 执行打包
4. 恢复原始 `pyproject.toml` 配置

## 🚀 一键打包流程

```bash
# 1. 运行打包脚本（自动处理配置切换）
python build_wheel.py

# 2. 检查生成的文件
ls dist/
# 应该看到:
# - droidrun-0.3.2-py3-none-any.whl
# - droidrun-0.3.2.tar.gz

# 3. 本地安装测试
pip install dist/droidrun-0.3.2-py3-none-any.whl --force-reinstall

# 4. 验证安装
pip show droidrun
python -c "import droidrun; print(f'✅ DroidRun {droidrun.__version__} 安装成功！')"
```

### 脚本执行过程

`build_wheel.py` 会自动执行以下步骤：

1. **🧹 清理构建文件**
   - 删除 `build/`, `dist/`, `*.egg-info/` 目录

2. **🔨 创建 Wheel 包**
   - 使用 setuptools 手动构建 wheel
   - 生成 `droidrun-0.3.2-py3-none-any.whl`

3. **📦 创建源码包**
   - 打包源码为 `droidrun-0.3.2.tar.gz`

4. **🔍 验证包完整性**
   - 检查生成的文件
   - 显示文件大小

## 📁 生成的文件说明

打包完成后，`dist/` 目录包含：

```
dist/
├── droidrun-0.3.2-py3-none-any.whl    # Wheel 包（推荐安装）
└── droidrun-0.3.2.tar.gz              # 源码包
```

### Wheel 包 (.whl)
- **用途**: 快速安装，包含编译好的代码
- **安装**: `pip install droidrun-0.3.2-py3-none-any.whl`
- **优点**: 安装速度快，无需编译

### 源码包 (.tar.gz)
- **用途**: 源码分发，需要编译安装
- **安装**: `pip install droidrun-0.3.2.tar.gz`
- **优点**: 体积小，兼容性好

## 🎯 安装后的目录结构

使用 wheel 包安装后，会在 Python 环境中生成：

```
site-packages/
├── droidrun/                           # 主包目录
│   ├── __init__.py
│   ├── agent/
│   ├── tools/
│   ├── adb/
│   └── ...
└── droidrun-0.3.2.dist-info/          # 您要的 .dist-info 目录
    ├── METADATA                        # 包元数据
    ├── WHEEL                           # Wheel 信息
    ├── RECORD                          # 安装记录
    ├── INSTALLER                       # 安装工具
    ├── top_level.txt                   # 顶级包名
    └── entry_points.txt                # 命令行入口
```

## 🔄 完整打包工作流

### 1. 准备阶段
```bash
# 确保在项目根目录
cd droidrun_diy

# 检查 Python 版本（需要 3.10+）
python --version

# 检查打包项目结构
python check_package.py
```

### 2. 打包阶段
```bash
# 使用推荐脚本打包
python build_wheel.py
```

### 3. 测试阶段
```bash
# 本地安装测试
pip install dist/droidrun-0.3.2-py3-none-any.whl --force-reinstall

# 验证安装
pip show droidrun
pip show -f droidrun  # 查看所有安装文件

# 测试导入
python -c "
import droidrun
from droidrun import AdbTools, DroidAgent, set_language, Language
print(f'✅ DroidRun {droidrun.__version__} 工作正常！')
"
```

### 4. 分发阶段
```bash
# 将 wheel 文件分享给其他用户
# 其他用户可以直接安装：
# pip install droidrun-0.3.2-py3-none-any.whl
```

## 🛠️ 开发者模式（推荐开发使用）

如果您在开发过程中频繁修改代码，推荐使用开发模式：

```bash
# 可编辑安装，代码修改后立即生效
pip install -e .

# 安装开发依赖
pip install -e .[dev]

# 修改代码后无需重新安装，直接测试
python -c "import droidrun; print('测试修改')"
```

## ❓ 常见问题

### Q: 打包失败，提示缺少模块
A: 确保安装了基本构建工具：
```bash
pip install setuptools wheel
```

### Q: 想要更新版本号
A: 修改以下文件中的版本号：
- `pyproject.toml` (第3行)
- `droidrun/__init__.py` (第5行)

### Q: 想要添加新的依赖
A: 修改 `pyproject.toml` 中的 `dependencies` 列表

### Q: 打包的文件太大
A: 检查 `MANIFEST.in` 和 `pyproject.toml` 中的排除规则

## 📋 快速命令参考

```bash
# 打包
python build_wheel.py

# 快速安装
pip install dist/droidrun-0.3.2-py3-none-any.whl --force-reinstall

# 新环境-完整安装命令
uv venv --python 3.10.14
uv init
# 激活虚拟环境 (Windows系统)    Mac/Linux系统为: source .venv/bin/activate
.venv\Scripts\activate
uv add pip setuptools wheel
uv pip install droidrun-0.3.2-py3-none-any.whl --force-reinstall
uv add llama-index posthog==6.0.2 llama-index-llms-google-genai llama-index-llms-litellm>=0.5.1

# 验证
pip show droidrun

# 卸载
pip uninstall droidrun

# 开发模式
pip install -e .
```

---

**总结**: 使用 `python build_wheel.py` 进行本地打包，生成的 `.whl` 文件可以直接安装，会自动创建标准的 `.dist-info` 目录结构。
