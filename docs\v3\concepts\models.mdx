---
title: 'Supported Models'
description: 'DroidRun is **LLM agnostic**, which means you can use every LLM provider there is. 

Under the hood, DroidRun leverages [LlamaIndex LLM providers](https://docs.llamaindex.ai/en/stable/api_reference/llms/) to provide seamless integration with all major language model providers. This means you can easily switch between different providers or use multiple providers simultaneously depending on your needs.
'
---

## Install LLM Provider
```bash
# Install the package for your preffered LLM Provider
pip install llama-index-llms-[PROVIDER]
```

## Supported Providers

DroidRun supports all major LLM providers through LlamaIndex, including but not limited to:

- **OpenAI** (GPT-4o, GPT-o3, etc.)
- **Anthropic** (Claude models)
- **GoogleGenAI** (Gemini)
- **Azure OpenAI Service**
- **AWS Bedrock**
- **Cohere**
- **Hugging Face**
- **Local models** (Ollama, vLLM, etc.)
- And many more...

This flexibility allows you to choose the best model for your specific use case, budget, and performance requirements while maintaining the same DroidRun interface across all providers.
