"""
ADB包装器 - 用于Android设备控制的轻量级ADB包装器。
"""

import asyncio
import os
import shlex
from typing import Dict, List, Optional, Tuple

class ADBWrapper:
    """用于Android设备控制的轻量级ADB包装器。"""

    def __init__(self, adb_path: Optional[str] = None):
        """初始化ADB包装器。

        Args:
            adb_path: ADB二进制文件路径（默认为PATH中的'adb'）
        """
        self.adb_path = adb_path or "adb"
        self._devices_cache: List[Dict[str, str]] = []

    async def _run_command(
        self,
        args: List[str],
        timeout: Optional[float] = None,
        check: bool = True
    ) -> Tuple[str, str]:
        """运行ADB命令。

        Args:
            args: 命令参数
            timeout: 命令超时时间（秒）
            check: 是否检查返回码

        Returns:
            (stdout, stderr)的元组
        """
        cmd = [self.adb_path, *args]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            if timeout is not None:
                stdout_bytes, stderr_bytes = await asyncio.wait_for(
                    process.communicate(), timeout
                )
            else:
                stdout_bytes, stderr_bytes = await process.communicate()

            stdout = stdout_bytes.decode("utf-8", errors="replace").strip()
            stderr = stderr_bytes.decode("utf-8", errors="replace").strip()

            if check and process.returncode != 0:
                raise RuntimeError(f"ADB命令失败: {stderr or stdout}")

            return stdout, stderr

        except asyncio.TimeoutError:
            raise TimeoutError(f"ADB命令超时: {' '.join(cmd)}")
        except FileNotFoundError:
            raise FileNotFoundError(f"在 {self.adb_path} 找不到ADB")

    async def _run_device_command(
        self,
        serial: str,
        args: List[str],
        timeout: Optional[float] = None,
        check: bool = True
    ) -> Tuple[str, str]:
        """为特定设备运行ADB命令。"""
        return await self._run_command(["-s", serial, *args], timeout, check)

    async def get_devices(self) -> List[Dict[str, str]]:
        """获取已连接设备列表。

        Returns:
            包含'serial'和'status'键的设备信息字典列表
        """
        stdout, _ = await self._run_command(["devices", "-l"])

        devices = []
        for line in stdout.splitlines()[1:]:  # 跳过第一行（标题）
            if not line.strip():
                continue

            parts = line.split()
            if not parts:
                continue

            serial = parts[0]
            status = parts[1] if len(parts) > 1 else "unknown"
            
            devices.append({
                "serial": serial,
                "status": status
            })
            
        self._devices_cache = devices
        return devices

    async def connect(self, host: str, port: int = 5555) -> str:
        """通过TCP/IP连接到设备。

        Args:
            host: 设备IP地址
            port: 设备端口

        Returns:
            设备序列号 (host:port)
        """
        serial = f"{host}:{port}"

        # 检查是否已连接
        devices = await self.get_devices()
        if any(d["serial"] == serial for d in devices):
            return serial

        stdout, _ = await self._run_command(["connect", serial], timeout=10.0)

        if "connected" not in stdout.lower():
            raise RuntimeError(f"连接失败: {stdout}")

        return serial

    async def disconnect(self, serial: str) -> bool:
        """断开与设备的连接。

        Args:
            serial: 设备序列号

        Returns:
            如果成功断开连接则返回True
        """
        try:
            stdout, _ = await self._run_command(["disconnect", serial])
            return "disconnected" in stdout.lower()
        except Exception:
            return False

    async def shell(self, serial: str, command: str, timeout: Optional[float] = None) -> str:
        """在设备上运行shell命令。

        Args:
            serial: 设备序列号
            command: 要运行的shell命令
            timeout: 命令超时时间（秒）

        Returns:
            命令输出
        """
        stdout, _ = await self._run_device_command(serial, ["shell", command], timeout=timeout)
        return stdout

    async def get_properties(self, serial: str) -> Dict[str, str]:
        """获取设备属性。

        Args:
            serial: 设备序列号

        Returns:
            设备属性字典
        """
        output = await self.shell(serial, "getprop")
        
        properties = {}
        for line in output.splitlines():
            if not line or "[" not in line or "]" not in line:
                continue
                
            try:
                key = line.split("[")[1].split("]")[0]
                value = line.split("[")[2].split("]")[0]
                properties[key] = value
            except IndexError:
                continue
                
        return properties
        
    async def install_app(
        self,
        serial: str,
        apk_path: str,
        reinstall: bool = False,
        grant_permissions: bool = True
    ) -> Tuple[str, str]:
        """在设备上安装APK。

        Args:
            serial: 设备序列号
            apk_path: APK文件路径
            reinstall: 如果应用存在是否重新安装
            grant_permissions: 是否授予所有权限

        Returns:
            (stdout, stderr)的元组
        """
        args = ["install"]
        if reinstall:
            args.append("-r")
        if grant_permissions:
            args.append("-g")
        args.append(apk_path)
        
        return await self._run_device_command(serial, args, timeout=120.0)
        
    async def pull_file(self, serial: str, device_path: str, local_path: str) -> Tuple[str, str]:
        """从设备拉取文件。

        Args:
            serial: 设备序列号
            device_path: 设备上的路径
            local_path: 本地机器上的路径

        Returns:
            (stdout, stderr)的元组
        """
        # 如果目录不存在则创建
        local_dir = os.path.dirname(local_path)
        if local_dir and not os.path.exists(local_dir):
            os.makedirs(local_dir)
            
        return await self._run_device_command(serial, ["pull", device_path, local_path], timeout=60.0) 