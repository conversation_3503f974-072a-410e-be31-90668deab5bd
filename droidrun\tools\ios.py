"""
UI操作 - 用于iOS设备控制的核心UI交互工具。
"""

import re
import time
import asyncio
from typing import Optional, Dict, Tuple, List, Any
import logging
import aiohttp
from droidrun.tools.tools import Tools

# 为IOS模块获取一个日志记录器
logger = logging.getLogger("IOS")

SYSTEM_BUNDLE_IDENTIFIERS = [
    "ai.droidrun.droidrun-ios-portal",
    "com.apple.Bridge",
    "com.apple.DocumentsApp",
    "com.apple.Fitness",
    "com.apple.Health",
    "com.apple.Maps",
    "com.apple.MobileAddressBook",
    "com.apple.MobileSMS",
    "com.apple.Passbook",
    "com.apple.Passwords",
    "com.apple.Preferences",
    "com.apple.PreviewShell",
    "com.apple.mobilecal",
    "com.apple.mobilesafari",
    "com.apple.mobileslideshow",
    "com.apple.news",
    "com.apple.reminders",
    "com.apple.shortcuts",
    "com.apple.webapp",
]


class IOSTools(Tools):
    """用于iOS设备控制的核心UI交互工具。"""

    def __init__(self, url: str, bundle_identifiers: List[str] = []) -> None:
        """初始化IOSTools实例。

        参数:
            url: iOS设备URL。这是iOS设备的URL。它用于向iOS设备发送请求。
            bundle_identifiers: 要包含在包列表中的包标识符列表
        """
        self.clickable_elements_cache: List[Dict[str, Any]] = []
        self.url = url
        self.last_screenshot = None
        self.reason = None
        self.success = None
        self.finished = False
        self.memory: List[str] = []
        self.screenshots: List[Dict[str, Any]] = []
        self.last_tapped_rect: Optional[str] = (
            None  # 存储最后点击的元素的矩形区域，用于文本输入
        )
        self.bundle_identifiers = bundle_identifiers
        logger.info(f"iOS设备URL: {url}")

    async def get_state(
        self, serial: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        使用辅助功能API从iOS设备获取所有可点击的UI元素。

        参数:
            serial: 可选的设备URL（对iOS不使用，使用实例URL）

        返回:
            包含从设备屏幕提取的UI元素的字典列表
        """
        try:
            async with aiohttp.ClientSession() as session:
                a11y_url = f"{self.url}/vision/a11y"
                async with session.get(a11y_url) as response:
                    if response.status == 200:
                        a11y_data = await response.json()

                        # 解析iOS辅助功能树格式
                        elements = self._parse_ios_accessibility_tree(
                            a11y_data["accessibilityTree"]
                        )

                        # 缓存元素以供tap_by_index使用
                        self.clickable_elements_cache = elements

                        return {
                            "a11y_tree":self.clickable_elements_cache
                        }
                    else:
                        logger.error(
                            f"获取辅助功能数据失败: HTTP {response.status}"
                        )
                        raise ValueError(
                            f"获取辅助功能数据失败: HTTP {response.status}"
                        )

        except Exception as e:
            logger.error(f"获取可点击元素时出错: {e}")
            # raise ValueError(f"获取可点击元素时出错: {e}")

    def _parse_ios_accessibility_tree(self, a11y_data: str) -> List[Dict[str, Any]]:
        """
        将iOS辅助功能树格式解析为结构化元素。

        参数:
            a11y_data: 来自iOS设备的原始辅助功能数据

        返回:
            带有坐标和属性的已解析UI元素列表
        """
        elements = []
        lines = a11y_data.strip().split("\n")

        # 跟踪当前元素索引
        element_index = 0

        for line in lines:
            # 跳过空行和标题行
            if (
                not line.strip()
                or line.startswith("Attributes:")
                or line.startswith("Element subtree:")
                or line.startswith("Path to element:")
                or line.startswith("Query chain:")
            ):
                continue

            # 解析UI元素 - 查找带坐标的行
            # 格式：ElementType, {{x, y}, {width, height}}, [可选属性]
            coord_match = re.search(
                r"\{\{([0-9.]+),\s*([0-9.]+)\},\s*\{([0-9.]+),\s*([0-9.]+)\}\}", line
            )

            if coord_match:
                x, y, width, height = map(float, coord_match.groups())

                # 提取元素类型（第一个逗号之前的文本）
                element_type_match = re.match(r"\s*(.+?),", line)
                element_type = (
                    element_type_match.group(1).strip()
                    if element_type_match
                    else "未知"
                )

                # 移除前导箭头和空格
                element_type = re.sub(r"^[→\s]+", "", element_type)

                # 如果存在，提取标签
                label_match = re.search(r"label:\s*'([^']*)'", line)
                label = label_match.group(1) if label_match else ""

                # 如果存在，提取标识符
                identifier_match = re.search(r"identifier:\s*'([^']*)'", line)
                identifier = identifier_match.group(1) if identifier_match else ""

                # 如果存在，提取占位符值
                placeholder_match = re.search(r"placeholderValue:\s*'([^']*)'", line)
                placeholder = placeholder_match.group(1) if placeholder_match else ""

                # 如果存在，提取值
                value_match = re.search(r"value:\s*([^,}]+)", line)
                value = value_match.group(1).strip() if value_match else ""

                # 为iOS点击API计算矩形字符串（x,y,width,height格式）
                rect_str = f"{x},{y},{width},{height}"

                # 创建元素字典
                element = {
                    "index": element_index,
                    "type": element_type,
                    "className": element_type,
                    "text": label or identifier or placeholder or "",
                    "label": label,
                    "identifier": identifier,
                    "placeholder": placeholder,
                    "value": value,
                    "bounds": f"{x},{y},{x+width},{y+height}",  # left,top,right,bottom格式以保持兼容性
                    "rect": rect_str,  # x,y,width,height格式用于iOS API
                    "x": x,
                    "y": y,
                    "width": width,
                    "height": height,
                    "center_x": x + width / 2,
                    "center_y": y + height / 2,
                }

                # 只包含交互式元素（按钮、文本字段等）
                interactive_types = [
                    "Button",
                    "SearchField",
                    "TextField",
                    "Cell",
                    "Switch",
                    "Slider",
                    "Stepper",
                    "Picker",
                    "Link",
                ]
                if any(
                    interactive_type in element_type
                    for interactive_type in interactive_types
                ):
                    elements.append(element)
                    element_index += 1

        return elements

    async def tap_by_index(self, index: int, serial: Optional[str] = None) -> str:
        """
        通过索引点击UI元素。

        此函数使用缓存的可点击元素
        找到具有给定索引的元素并点击其中心坐标。

        参数:
            index: 要点击的元素的索引

        返回:
            结果消息
        """

        def find_element_by_index(elements, target_index):
            """查找具有给定索引的元素。"""
            for item in elements:
                if item.get("index") == target_index:
                    return item
                # 如果存在子元素，则检查子元素
                children = item.get("children", [])
                result = find_element_by_index(children, target_index)
                if result:
                    return result
            return None

        try:
            # 检查是否有缓存的元素
            if not self.clickable_elements_cache:
                return "错误：没有缓存的UI元素。请先调用get_clickables。"

            # 查找具有给定索引的元素
            element = find_element_by_index(self.clickable_elements_cache, index)

            if not element:
                # 列出可用的索引以帮助用户
                indices = [
                    elem.get("index")
                    for elem in self.clickable_elements_cache
                    if elem.get("index") is not None
                ]
                indices_str = ", ".join(str(idx) for idx in sorted(indices)[:20])
                if len(indices) > 20:
                    indices_str += f"... 还有 {len(indices) - 20} 个"

                return f"错误：未找到索引为 {index} 的元素。可用索引：{indices_str}"

            # 获取元素坐标
            x = element.get("x", 0)
            y = element.get("y", 0)
            width = element.get("width", 0)
            height = element.get("height", 0)

            if not all(
                [x is not None, y is not None, width is not None, height is not None]
            ):
                element_text = element.get("text", "无文本")
                element_class = element.get("className", "未知类")
                return f"错误：索引为 {index} 的元素（'{element_text}'，{element_class}）没有坐标，无法点击"

            # 以iOS格式格式化矩形：{{x,y},{w,h}}
            ios_rect = f"{{{{{x},{y}}},{{{width},{height}}}}}"

            # 存储矩形以供潜在的文本输入（保持简单格式用于文本输入）
            self.last_tapped_rect = f"{x},{y},{width},{height}"

            # 发出点击请求
            async with aiohttp.ClientSession() as session:
                tap_url = f"{self.url}/gestures/tap"
                payload = {"rect": ios_rect, "count": 1, "longPress": False}

                logger.info(f"载荷 {payload}")

                async with session.post(tap_url, json=payload) as response:
                    if response.status == 200:
                        # 添加小延迟以允许UI更新
                        await asyncio.sleep(0.5)

                        # 创建描述性响应
                        response_parts = []
                        response_parts.append(f"点击了索引为 {index} 的元素")
                        response_parts.append(
                            f"文本：'{element.get('text', '无文本')}'"
                        )
                        response_parts.append(
                            f"类：{element.get('className', '未知类')}"
                        )
                        response_parts.append(f"矩形：{ios_rect}")

                        return " | ".join(response_parts)
                    else:
                        return f"错误：点击元素失败。HTTP {response.status}"

        except Exception as e:
            return f"错误：{str(e)}"

    """async def tap_by_coordinates(self, x: int, y: int) -> bool:
         # 以iOS格式格式化矩形：{{x,y},{w,h}}
        width = 1
        height = 1
        ios_rect = f"{{{{{x},{y}}},{{{width},{height}}}}}"

        # 发出点击请求
        async with aiohttp.ClientSession() as session:
            tap_url = f"{self.url}/gestures/tap"
            payload = {"rect": ios_rect, "count": 1, "longPress": False}

            logger.info(f"载荷 {payload}")

            async with session.post(tap_url, json=payload) as response:
                if response.status == 200:
                    return True
                else:
                    return False"""

    async def tap(self, index: int) -> str:
        """
        通过索引点击UI元素。

        此函数使用上次get_clickables调用的缓存可点击元素
        找到具有给定索引的元素并点击其中心坐标。

        参数:
            index: 要点击的元素的索引

        返回:
            结果消息
        """
        return await self.tap_by_index(index)

    async def swipe(
        self, start_x: int, start_y: int, end_x: int, end_y: int, duration_ms: int = 300
    ) -> bool:
        """
        在设备屏幕上执行直线滑动手势。
        要执行按住（长按），请将起始和结束坐标设置为相同的值，并根据需要增加持续时间。
        参数:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration_ms: 滑动持续时间（毫秒）（在iOS API中不使用）
        返回:
            表示成功或失败的布尔值
        """
        try:
            # 根据坐标计算滑动方向
            dx = end_x - start_x
            dy = end_y - start_y

            # 确定主要方向
            if abs(dx) > abs(dy):
                direction = "right" if dx > 0 else "left"
            else:
                direction = "down" if dy > 0 else "up"

            async with aiohttp.ClientSession() as session:
                swipe_url = f"{self.url}/gestures/swipe"
                payload = {"x": float(start_x), "y": float(start_y), "dir": direction}

                async with session.post(swipe_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(
                            f"从 ({start_x}, {start_y}) 滑动到 ({end_x}, {end_y}) 方向：{direction}"
                        )
                        return True
                    else:
                        logger.error(f"滑动失败：HTTP {response.status}")
                        return False

        except Exception as e:
            logger.error(f"执行滑动时出错：{e}")
            return False

    async def drag_and_drop(
        self, start_x: int, start_y: int, end_x: int, end_y: int, hold_duration_ms: int = 500
    ) -> bool:
        """
        在iOS设备上执行拖拽操作。
        注意：iOS的拖拽实现可能与Android不同，这里提供基本的拖拽支持。

        参数:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 目标X坐标
            end_y: 目标Y坐标
            hold_duration_ms: 长按持续时间（iOS API可能不直接支持）

        返回:
            表示成功或失败的布尔值
        """
        try:
            # iOS的拖拽实现：先长按，再拖动
            # 注意：这是一个简化的实现，实际的iOS拖拽可能需要更复杂的API调用

            # 步骤1: 长按起始位置
            logger.info(f"iOS拖拽：在 ({start_x}, {start_y}) 长按")
            async with aiohttp.ClientSession() as session:
                # 使用tap API进行长按（通过延迟模拟）
                tap_url = f"{self.url}/inputs/tap"
                payload = {"x": float(start_x), "y": float(start_y)}

                async with session.post(tap_url, json=payload) as response:
                    if response.status != 200:
                        logger.error(f"长按失败：HTTP {response.status}")
                        return False

                # 等待长按效果
                await asyncio.sleep(hold_duration_ms / 1000.0)

                # 步骤2: 拖动到目标位置
                logger.info(f"iOS拖拽：从 ({start_x}, {start_y}) 拖动到 ({end_x}, {end_y})")

                # 使用滑动API执行拖动
                dx = end_x - start_x
                dy = end_y - start_y

                if abs(dx) > abs(dy):
                    direction = "right" if dx > 0 else "left"
                else:
                    direction = "down" if dy > 0 else "up"

                swipe_url = f"{self.url}/inputs/swipe"
                swipe_payload = {"x": float(start_x), "y": float(start_y), "dir": direction}

                async with session.post(swipe_url, json=swipe_payload) as response:
                    if response.status == 200:
                        logger.info(f"iOS拖拽完成：从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
                        return True
                    else:
                        logger.error(f"拖动失败：HTTP {response.status}")
                        return False

        except Exception as e:
            logger.error(f"iOS拖拽操作出错：{e}")
            return False

    async def input_text(self, text: str, serial: Optional[str] = None) -> str:
        """
        在iOS设备上输入文本。

        参数:
            text: 要输入的文本。可以包含空格、换行符和特殊字符，包括非ASCII字符。
            serial: 可选的设备序列号（对iOS不使用，使用实例URL）

        返回:
            结果消息
        """
        try:
            # 如果可用，使用最后点击的元素的矩形，否则使用默认值
            rect = self.last_tapped_rect if self.last_tapped_rect else "0,0,100,100"

            async with aiohttp.ClientSession() as session:
                type_url = f"{self.url}/inputs/type"
                payload = {"rect": rect, "text": text}

                async with session.post(type_url, json=payload) as response:
                    if response.status == 200:
                        await asyncio.sleep(0.5)  # 等待文本输入完成
                        return f"文本输入完成：{text[:50]}{'...' if len(text) > 50 else ''}"
                    else:
                        return f"错误：输入文本失败。HTTP {response.status}"

        except Exception as e:
            return f"发送文本输入时出错：{str(e)}"

    async def back(self) -> str:
        raise NotImplementedError("iOS尚未实现返回功能")

    async def press_key(self, keycode: int) -> str:
        # TODO: 重构这个。它不是关于物理按键，而是返回、回车、删除等。
        """
        在iOS设备上按键。

        iOS键码：
        - 0: HOME（主页）
        - 4: ACTION（操作）
        - 5: CAMERA（相机）

        参数:
            keycode: 要按下的iOS键码
        """
        try:
            key_names = {0: "HOME", 4: "ACTION", 5: "CAMERA"}
            key_name = key_names.get(keycode, str(keycode))

            async with aiohttp.ClientSession() as session:
                key_url = f"{self.url}/inputs/key"
                payload = {"key": keycode}

                async with session.post(key_url, json=payload) as response:
                    if response.status == 200:
                        return f"按下了键 {key_name}"
                    else:
                        return f"错误：按键失败。HTTP {response.status}"

        except Exception as e:
            return f"按键时出错：{str(e)}"

    async def start_app(self, package: str, activity: str = "") -> str:
        """
        在iOS设备上启动应用程序。

        参数:
            package: 包标识符（例如，"com.apple.MobileSMS"）
            activity: 可选的活动名称（在iOS上不使用）
        """
        try:
            async with aiohttp.ClientSession() as session:
                launch_url = f"{self.url}/inputs/launch"
                payload = {"bundleIdentifier": package}

                async with session.post(launch_url, json=payload) as response:
                    if response.status == 200:
                        await asyncio.sleep(1.0)  # 等待应用程序启动
                        return f"成功启动应用程序：{package}"
                    else:
                        return f"错误：启动应用程序 {package} 失败。HTTP {response.status}"

        except Exception as e:
            return f"启动应用程序时出错：{str(e)}"

    async def take_screenshot(self) -> Tuple[str, bytes]:
        """
        对iOS设备进行截图。
        此函数捕获当前屏幕并在下一条消息中将截图添加到上下文中。
        还将截图与时间戳一起存储在截图列表中，以便稍后创建GIF。
        """
        try:
            async with aiohttp.ClientSession() as session:
                screenshot_url = f"{self.url}/vision/screenshot"
                async with session.get(screenshot_url) as response:
                    if response.status == 200:
                        screenshot_data = await response.read()

                        # 存储带时间戳的截图
                        screenshot_info = {
                            "timestamp": time.time(),
                            "data": screenshot_data,
                        }
                        self.screenshots.append(screenshot_info)
                        self.last_screenshot = screenshot_data

                        logger.info(
                            f"截图成功捕获，大小：{len(screenshot_data)} 字节"
                        )
                        return ("PNG", screenshot_data)
                    else:
                        logger.error(
                            f"捕获截图失败：HTTP {response.status}"
                        )
                        raise ValueError(
                            f"捕获截图失败：HTTP {response.status}"
                        )

        except Exception as e:
            logger.error(f"捕获截图时出错：{e}")
            raise ValueError(f"截图时出错：{str(e)}")

    async def get_phone_state(self, serial: Optional[str] = None) -> Dict[str, Any]:
        """
        获取当前手机状态，包括当前活动和键盘可见性。

        参数:
            serial: 可选的设备序列号（对iOS不使用）

        返回:
            包含当前手机状态信息的字典
        """
        try:
            # 对于iOS，我们可以从辅助功能API获取一些状态信息
            async with aiohttp.ClientSession() as session:
                a11y_url = f"{self.url}/vision/state"
                async with session.get(a11y_url) as response:
                    if response.status == 200:
                        state_data = await response.json()

                        return {
                            "current_activity": state_data["activity"],
                            "keyboard_shown": state_data["keyboardShown"],
                        }
                    else:
                        return {
                            "error": f"获取设备状态失败：HTTP {response.status}",
                            "current_activity": "未知",
                            "keyboard_shown": False,
                        }

        except Exception as e:
            return {"error": str(e), "message": f"获取手机状态时出错：{str(e)}"}

    async def list_packages(self, include_system_apps: bool = True) -> List[str]:
        """
        列出设备上的包。

        参数:
            include_system_apps: 是否包含系统应用程序（默认：True）

        返回:
            包标识符列表
        """
        all_packages = set(self.bundle_identifiers)
        if include_system_apps:
            all_packages.update(SYSTEM_BUNDLE_IDENTIFIERS)
        return sorted(list(all_packages))


    async def remember(self, information: str) -> str:
        """
        存储重要信息以供将来上下文使用。

        此信息将包含在未来的LLM提示中，以帮助在交互中保持上下文。
        将此用于关键事实、观察或应影响未来决策的用户偏好。

        参数:
            information: 要记住的信息

        返回:
            确认消息
        """
        if not information or not isinstance(information, str):
            return "错误：请提供有效的信息来记住。"

        # 将信息添加到内存中
        self.memory.append(information.strip())

        # 限制内存大小以防止上下文溢出（保留最近的项目）
        max_memory_items = 10
        if len(self.memory) > max_memory_items:
            self.memory = self.memory[-max_memory_items:]

        return f"已记住：{information}"

    def get_memory(self) -> List[str]:
        """
        检索所有存储的内存项目。

        返回:
            存储的内存项目列表
        """
        return self.memory.copy()

    def complete(self, success: bool, reason: str = ""):
        """
        将任务标记为已完成。

        参数:
            success: 指示任务是否成功。
            reason: 失败/成功的原因
        """
        if success:
            self.success = True
            self.reason = reason or "任务成功完成。"
            self.finished = True
        else:
            self.success = False
            if not reason:
                raise ValueError("如果success为False，则需要失败原因。")
            self.reason = reason
            self.finished = True
