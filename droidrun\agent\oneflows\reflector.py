from llama_index.core.llms.llm import LLM
from droidrun.agent.context import EpisodicMemory
from droidrun.agent.context.reflection import Reflection
from llama_index.core.base.llms.types import ChatMessage, ImageBlock
from droidrun.agent.utils.chat_utils import add_screenshot_image_block
from droidrun.agent.context.agent_persona import <PERSON><PERSON><PERSON><PERSON>
import j<PERSON>
from typing import Dict, Any, List, Optional
import logging
from PIL import Image, ImageDraw, ImageFont
import io

logger = logging.getLogger("droidrun")

class Reflector:
    def __init__(
        self,
        llm: LLM,
        debug: bool = False,
        *args,
        **kwargs
    ):
        self.llm = llm
        self.debug = debug

    async def reflect_on_episodic_memory(self, episodic_memory: EpisodicMemory, goal: str) -> Reflection:
        """分析情节记忆并提供对代理性能的反思。"""
        system_prompt_content = self._create_system_prompt()
        system_prompt = ChatMessage(role="system", content=system_prompt_content)

        episodic_memory_content = self._format_episodic_memory(episodic_memory)
        persona_content = self._format_persona(episodic_memory.persona)
        
        # 创建包含角色信息的用户消息内容
        user_content = f"{persona_content}\n\n目标: {goal}\n\n情节记忆步骤:\n{episodic_memory_content}\n\n请评估目标是否已实现，并以指定的JSON格式提供您的分析。"

        # 创建用户消息
        user_message = ChatMessage(role="user", content=user_content)

        # 创建屏幕截图网格，如果存在屏幕截图则添加为ImageBlock
        screenshots_grid = self._create_screenshots_grid(episodic_memory)

        if screenshots_grid:
            # 使用add_screenshot_image_block函数正确添加图像
            messages_list = [system_prompt, user_message]
            messages_list = await add_screenshot_image_block(screenshots_grid, messages_list, copy=False)
            messages = messages_list
        else:
            messages = [system_prompt, user_message]
        response = await self.llm.achat(messages=messages)

        logger.info(f"反思 {response.message.content}")

        try:
            # 清理响应内容以处理markdown代码块
            content = response.message.content.strip()

            # 如果存在，移除markdown代码块格式
            if content.startswith('```json'):
                content = content[7:]  # 移除```json
            elif content.startswith('```'):
                content = content[3:]   # 移除```

            if content.endswith('```'):
                content = content[:-3]  # 移除尾部```
            
            content = content.strip()
            
            parsed_response = json.loads(content)
            return Reflection.from_dict(parsed_response)
        except json.JSONDecodeError as e:
            logger.error(f"解析反思响应失败: {e}")
            logger.error(f"原始响应: {response.message.content}")
            return await self.reflect_on_episodic_memory(episodic_memory=episodic_memory, goal=goal)
    
    def _create_screenshots_grid(self, episodic_memory: EpisodicMemory) -> Optional[bytes]:
        """从情节记忆步骤创建3x2屏幕截图网格。"""
        # 从步骤中提取屏幕截图
        screenshots = []
        for step in episodic_memory.steps:
            if step.screenshot:
                try:
                    # 将字节转换为PIL图像
                    screenshot_image = Image.open(io.BytesIO(step.screenshot))
                    screenshots.append(screenshot_image)
                except Exception as e:
                    logger.warning(f"加载屏幕截图失败: {e}")
                    continue
        
        if not screenshots:
            return None
        
        num_screenshots = min(len(screenshots), 6)
        cols, rows = num_screenshots, 1
        
        screenshots = screenshots[:num_screenshots]
        
        if not screenshots:
            return None
        
        if screenshots:
            cell_width = screenshots[0].width // 2
            cell_height = screenshots[0].height // 2
        else:
            return None
        
        # 定义标题栏高度
        header_height = 60

        # 创建网格图像，为标题栏留出空间
        grid_width = cols * cell_width
        grid_height = rows * (cell_height + header_height)
        grid_image = Image.new('RGB', (grid_width, grid_height), color='white')

        # 设置步骤文本的字体
        draw = ImageDraw.Draw(grid_image)
        try:
            # 为标题文本使用较大字体
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 48)
        except:
            font = ImageFont.load_default()
        
        # 在网格中放置屏幕截图和标题栏
        for i, screenshot in enumerate(screenshots):
            row = i // cols
            col = i % cols

            # 计算位置
            x = col * cell_width
            header_y = row * (cell_height + header_height)
            screenshot_y = header_y + header_height

            # 创建标题栏
            header_rect = [x, header_y, x + cell_width, header_y + header_height]
            draw.rectangle(header_rect, fill='#2c3e50')  # 深蓝色标题

            # 在标题栏中绘制步骤文本
            text = f"步骤 {i+1}"
            # 获取文本尺寸以居中
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 在标题栏中居中文本
            text_x = x + (cell_width - text_width) // 2
            text_y = header_y + (header_height - text_height) // 2

            draw.text((text_x, text_y), text, fill='white', font=font)

            # 调整大小并在标题下方放置屏幕截图
            resized_screenshot = screenshot.resize((cell_width, cell_height), Image.Resampling.LANCZOS)
            grid_image.paste(resized_screenshot, (x, screenshot_y))
        
        # 保存网格到磁盘用于调试（仅在启用调试标志时）
        if self.debug:
            import os
            from datetime import datetime

            # 如果调试目录不存在则创建
            debug_dir = "reflection_screenshots"
            os.makedirs(debug_dir, exist_ok=True)

            # 使用时间戳保存
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_filename = os.path.join(debug_dir, f"screenshot_grid_{timestamp}.png")
            grid_image.save(debug_filename)
            logger.info(f"屏幕截图网格已保存到: {debug_filename}")

        # 转换为字节以与add_screenshot_image_block一起使用
        buffer = io.BytesIO()
        grid_image.save(buffer, format='PNG')
        buffer.seek(0)
        
        return buffer.getvalue()

    def _create_system_prompt(self) -> str:
        """创建包含反思指令的系统提示。"""
        system_prompt = """您是一个分析Android代理性能的反思AI。您的角色是检查情节记忆步骤并评估代理是否实现了其目标。

        评估过程:
        1. 首先，根据情节记忆步骤确定代理是否实现了既定目标
        2. 如果目标已实现，确认成功
        3. 如果目标未实现，分析出了什么问题并提供直接建议
        4. 使用提供的屏幕截图（如果有）来理解每个步骤的视觉上下文
        屏幕截图显示代理看到的屏幕。按从左到右的时间顺序排列

        分析领域（针对失败的目标）:
        - 错过的机会或低效的行动
        - 错误的工具使用或导航选择
        - 未能理解上下文或用户意图
        - 次优的决策模式

        建议指导原则（针对失败的目标）:
        - 使用"您"的形式直接对代理说话，关注现在/未来（例如，"您需要..."，"寻找..."，"专注于..."）
        - 提供情境感知建议，帮助处理失败尝试后的当前状态
        - 为重试目标时现在要做什么提供可操作的指导，而不是之前出了什么问题
        - 考虑代理重试时将面临的当前应用状态和上下文
        - 专注于在当前情况下成功所需的关键策略或方法
        - 保持简洁但精确（1-2句话）

        输出格式:
        您必须以这种确切格式响应有效的JSON对象:

        {{
            "goal_achieved": true,
            "advice": null,
            "summary": "发生了什么的简要摘要"
        }}

        或者

        {{
            "goal_achieved": false,
            "advice": "使用'您'形式的直接建议，专注于当前情况 - 重试时您现在需要做什么",
            "summary": "发生了什么的简要摘要"
        }}

        重要提示:
        - 如果goal_achieved为true，将advice设置为null
        - 如果goal_achieved为false，提供专注于重试时在当前情况下现在要做什么的直接"您"形式建议
        - 建议应该是前瞻性和情境性的，而不是对过去错误的回顾
        - 始终包含代理性能的简要摘要
        - 确保JSON有效且可解析
        - 仅返回JSON对象，不要额外的文本或格式"""

        return system_prompt
    
    def _format_persona(self, persona: AgentPersona) -> str:
        """为用户提示格式化代理角色信息。"""
        persona_content = f"""代理角色信息:
        - 名称: {persona.name}
        - 描述: {persona.description}
        - 可用工具: {', '.join(persona.allowed_tools)}
        - 专业领域: {', '.join(persona.expertise_areas)}
        - 系统提示: {persona.system_prompt}"""
                
        return persona_content
    
    def _format_episodic_memory(self, episodic_memory: EpisodicMemory) -> str:
        """将情节记忆步骤格式化为可读格式以供分析。"""
        formatted_steps = []

        for i, step in enumerate(episodic_memory.steps, 1):
            try:
                # 解析JSON字符串以获取没有转义字符的原始内容
                chat_history = json.loads(step.chat_history)
                response = json.loads(step.response)


                formatted_step = f"""步骤 {i}:
            聊天历史: {json.dumps(chat_history, indent=2)}
            响应: {json.dumps(response, indent=2)}
            时间戳: {step.timestamp}
            ---"""
            except json.JSONDecodeError as e:
                # 如果JSON解析失败则回退到原始格式
                logger.warning(f"解析步骤 {i} 的JSON失败: {e}")
                formatted_step = f"""步骤 {i}:
            聊天历史: {step.chat_history}
            响应: {step.response}
            时间戳: {step.timestamp}
            ---"""
            formatted_steps.append(formatted_step)
        
        return "\n".join(formatted_steps)