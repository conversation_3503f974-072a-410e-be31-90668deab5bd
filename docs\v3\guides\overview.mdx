---
title: Overview
---

Welcome to the DroidRun Guides! Here you'll find step-by-step instructions and best practices for using DroidRun with different LLM providers, configuring telemetry, and more. Each guide is designed to help you get the most out of DroidRun, whether you're just getting started or looking to leverage advanced features.

---

## [CLI Usage Guide](./cli)
Learn how to control Android devices using natural language and LLM agents via the DroidRun command-line interface. This guide covers:
- Installing DroidRun and dependencies
- Connecting your Android device
- Using the CLI with different LLM providers (OpenAILike, Ollama, Gemini)
- Key CLI options and flags
- Troubleshooting common issues

---

## [Using DroidRun with Gemini](./gemini)
Learn to use DroidRun with [Gemini](https://ai.google.dev/gemini-api/docs/), Google's advanced cloud-based LLMs. This guide covers:
- What Gemini is and its capabilities
- Setting up Gemini API access
- Installing required Python packages
- Example: Automating Android with Gemini
- Troubleshooting and advanced configuration tips

---

## [Using DroidRun with OpenAILike](./openailike)
Learn to use DroidRun with OpenAILike-compatible LLMs (such as OpenAI's GPT models and compatible APIs). This guide covers:
- What OpenAILike means and supported providers
- Setting up API keys and access
- Installing required Python packages
- Example: Automating Android with OpenAILike LLMs
- Troubleshooting and advanced configuration tips

---

## [Using DroidRun with Ollama](./ollama)
Discover how to integrate DroidRun with [Ollama](https://ollama.com/), an open-source platform for running large language models locally. This guide explains:
- What Ollama is and why use it
- Setting up Ollama and pulling models
- Installing required Python packages
- Example: Automating Android with local LLMs
- Troubleshooting and tips for best performance

---

## [Telemetry Guide](./telemetry.mdx)
Understand how DroidRun uses anonymized telemetry to improve the framework. This guide explains:
- Why telemetry is important
- What data is (and isn't) collected
- How to disable or enable telemetry
- Our commitment to privacy

---

Need more help? Check out the [Quickstart](../quickstart) or [Core Concepts](../concepts/agent) for deeper dives into DroidRun's architecture and features.
