"""
DroidRun CLI - 通过大语言模型代理控制Android设备的命令行界面。
"""

import asyncio
import click
import os
import logging
import warnings
from contextlib import nullcontext
from rich.console import Console
from droidrun.agent.droid import DroidAgent
from droidrun.agent.utils.llm_picker import load_llm
from droidrun.adb import DeviceManager
from droidrun.tools import AdbTools, IOSTools
from functools import wraps
from droidrun.cli.logs import LogHandler
from droidrun.telemetry import print_telemetry_message
from droidrun.portal import (
    download_portal_apk,
    enable_portal_accessibility,
    PORTAL_PACKAGE_NAME,
    ping_portal,
)

# 抑制所有警告
warnings.filterwarnings("ignore")
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["GRPC_ENABLE_FORK_SUPPORT"] = "false"

console = Console()
device_manager = DeviceManager()


def configure_logging(goal: str, debug: bool):
    logger = logging.getLogger("droidrun")
    logger.handlers = []

    handler = LogHandler(goal)
    handler.setFormatter(
        logging.Formatter("%(levelname)s %(message)s", "%H:%M:%S")
        if debug
        else logging.Formatter("%(message)s", "%H:%M:%S")
    )
    logger.addHandler(handler)

    logger.setLevel(logging.DEBUG if debug else logging.INFO)
    logger.propagate = False

    return handler


def coro(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@coro
async def run_command(
    command: str,
    device: str | None,
    provider: str,
    model: str,
    steps: int,
    base_url: str,
    api_base: str,
    vision: bool,
    reasoning: bool,
    reflection: bool,
    tracing: bool,
    debug: bool,
    save_trajectory: bool = False,
    ios: bool = False,
    **kwargs,
):
    """使用自然语言在您的Android设备上运行命令。"""
    log_handler = configure_logging(command, debug)
    logger = logging.getLogger("droidrun")

    log_handler.update_step("正在初始化...")

    with log_handler.render() as live:
        try:
            logger.info(f"🚀 开始执行: {command}")
            print_telemetry_message()

            if not kwargs.get("temperature"):
                kwargs["temperature"] = 0

            log_handler.update_step("正在设置工具...")

            # 设备设置
            if device is None and not ios:
                logger.info("🔍 正在查找已连接的设备...")
                device_manager = DeviceManager()
                devices = await device_manager.list_devices()
                if not devices:
                    raise ValueError("未找到已连接的设备。")
                device = devices[0].serial
                logger.info(f"📱 使用设备: {device}")
            elif device is None and ios:
                raise ValueError(
                    "未指定iOS设备。请通过 --device 参数指定设备基础URL (http://device-ip:6643)"
                )
            else:
                logger.info(f"📱 使用设备: {device}")

            tools = AdbTools(serial=device) if not ios else IOSTools(url=device)

            # LLM设置
            log_handler.update_step("正在初始化LLM...")
            llm = load_llm(
                provider_name=provider,
                model=model,
                base_url=base_url,
                api_base=api_base,
                **kwargs,
            )
            logger.info(f"🧠 LLM就绪: {provider}/{model}")

            # 代理设置
            log_handler.update_step("正在初始化DroidAgent...")

            mode = "规划推理模式" if reasoning else "直接执行模式"
            logger.info(f"🤖 代理模式: {mode}")

            if tracing:
                logger.info("🔍 跟踪已启用")

            droid_agent = DroidAgent(
                goal=command,
                llm=llm,
                tools=tools,
                max_steps=steps,
                timeout=1000,
                vision=vision,
                reasoning=reasoning,
                reflection=reflection,
                enable_tracing=tracing,
                debug=debug,
                save_trajectories=save_trajectory,
            )

            logger.info("▶️  开始执行代理...")
            logger.info("按 Ctrl+C 停止")
            log_handler.update_step("正在运行代理...")

            try:
                handler = droid_agent.run()

                async for event in handler.stream_events():
                    log_handler.handle_event(event)
                result = await handler

            except KeyboardInterrupt:
                log_handler.is_completed = True
                log_handler.is_success = False
                log_handler.current_step = "用户停止"
                logger.info("⏹️ 用户停止")

            except Exception as e:
                log_handler.is_completed = True
                log_handler.is_success = False
                log_handler.current_step = f"错误: {e}"
                logger.error(f"💥 错误: {e}")
                if debug:
                    import traceback

                    logger.debug(traceback.format_exc())

        except Exception as e:
            log_handler.current_step = f"错误: {e}"
            logger.error(f"💥 设置错误: {e}")
            if debug:
                import traceback

                logger.debug(traceback.format_exc())


class DroidRunCLI(click.Group):
    def parse_args(self, ctx, args):
        # 如果第一个参数不是选项且不是已知命令，则视为'run'命令
        if args and not args[0].startswith("-") and args[0] not in self.commands:
            args.insert(0, "run")

        return super().parse_args(ctx, args)


@click.option("--device", "-d", help="设备序列号或IP地址", default=None)
@click.option(
    "--provider",
    "-p",
    help="LLM提供商 (OpenAI, Ollama, Anthropic, GoogleGenAI, DeepSeek)",
    default="GoogleGenAI",
)
@click.option(
    "--model",
    "-m",
    help="LLM模型名称",
    default="models/gemini-2.5-flash",
)
@click.option("--temperature", type=float, help="LLM温度参数", default=0.2)
@click.option("--steps", type=int, help="最大步骤数", default=15)
@click.option(
    "--base_url",
    "-u",
    help="API基础URL (例如 OpenRouter 或 Ollama)",
    default=None,
)
@click.option(
    "--api_base",
    help="API基础URL (例如 OpenAI, OpenAI-Like)",
    default=None,
)
@click.option(
    "--vision",
    is_flag=True,
    help="通过屏幕截图启用视觉功能",
    default=False,
)
@click.option(
    "--reasoning", is_flag=True, help="启用规划推理功能", default=False
)
@click.option(
    "--reflection",
    is_flag=True,
    help="启用反思步骤以提高推理能力",
    default=False,
)
@click.option(
    "--tracing", is_flag=True, help="启用Arize Phoenix跟踪", default=False
)
@click.option(
    "--debug", is_flag=True, help="启用详细调试日志", default=False
)
@click.option(
    "--save-trajectory",
    is_flag=True,
    help="将代理轨迹保存到文件",
    default=False,
)
@click.group(cls=DroidRunCLI)
def cli(
    device: str | None,
    provider: str,
    model: str,
    steps: int,
    base_url: str,
    api_base: str,
    temperature: float,
    vision: bool,
    reasoning: bool,
    reflection: bool,
    tracing: bool,
    debug: bool,
    save_trajectory: bool,
):
    """DroidRun - 通过大语言模型代理控制您的Android设备。"""
    pass


@cli.command()
@click.argument("command", type=str)
@click.option("--device", "-d", help="设备序列号或IP地址", default=None)
@click.option(
    "--provider",
    "-p",
    help="LLM提供商 (OpenAI, Ollama, Anthropic, GoogleGenAI, DeepSeek)",
    default="GoogleGenAI",
)
@click.option(
    "--model",
    "-m",
    help="LLM模型名称",
    default="models/gemini-2.5-flash",
)
@click.option("--temperature", type=float, help="LLM温度参数", default=0.2)
@click.option("--steps", type=int, help="最大步骤数", default=15)
@click.option(
    "--base_url",
    "-u",
    help="API基础URL (例如 OpenRouter 或 Ollama)",
    default=None,
)
@click.option(
    "--api_base",
    help="API基础URL (例如 OpenAI 或 OpenAI-Like)",
    default=None,
)
@click.option(
    "--vision",
    is_flag=True,
    help="通过屏幕截图启用视觉功能",
    default=False,
)
@click.option(
    "--reasoning", is_flag=True, help="启用规划推理功能", default=False
)
@click.option(
    "--reflection",
    is_flag=True,
    help="启用反思步骤以提高推理能力",
    default=False,
)
@click.option(
    "--tracing", is_flag=True, help="启用Arize Phoenix跟踪", default=False
)
@click.option(
    "--debug", is_flag=True, help="启用详细调试日志", default=False
)
@click.option(
    "--save-trajectory",
    is_flag=True,
    help="将代理轨迹保存到文件",
    default=False,
)
@click.option("--ios", is_flag=True, help="在iOS设备上运行", default=False)
def run(
    command: str,
    device: str | None,
    provider: str,
    model: str,
    steps: int,
    base_url: str,
    api_base: str,
    temperature: float,
    vision: bool,
    reasoning: bool,
    reflection: bool,
    tracing: bool,
    debug: bool,
    save_trajectory: bool,
    ios: bool,
):
    """使用自然语言在您的Android设备上运行命令。"""
    # 调用我们的独立函数
    return run_command(
        command,
        device,
        provider,
        model,
        steps,
        base_url,
        api_base,
        vision,
        reasoning,
        reflection,
        tracing,
        debug,
        temperature=temperature,
        save_trajectory=save_trajectory,
        ios=ios,
    )


@cli.command()
@coro
async def devices():
    """列出已连接的Android设备。"""
    try:
        devices = await device_manager.list_devices()
        if not devices:
            console.print("[yellow]没有连接的设备。[/]")
            return

        console.print(f"[green]找到 {len(devices)} 个已连接的设备:[/]")
        for device in devices:
            console.print(f"  • [bold]{device.serial}[/]")
    except Exception as e:
        console.print(f"[red]列出设备时出错: {e}[/]")


@cli.command()
@click.argument("serial")
@click.option("--port", "-p", default=5555, help="ADB端口 (默认: 5555)")
@coro
async def connect(serial: str, port: int):
    """通过TCP/IP连接到设备。"""
    try:
        device = await device_manager.connect(serial, port)
        if device:
            console.print(f"[green]成功连接到 {serial}:{port}[/]")
        else:
            console.print(f"[red]连接到 {serial}:{port} 失败[/]")
    except Exception as e:
        console.print(f"[red]连接设备时出错: {e}[/]")


@cli.command()
@click.argument("serial")
@coro
async def disconnect(serial: str):
    """断开与设备的连接。"""
    try:
        success = await device_manager.disconnect(serial)
        if success:
            console.print(f"[green]成功断开与 {serial} 的连接[/]")
        else:
            console.print(f"[yellow]设备 {serial} 未连接[/]")
    except Exception as e:
        console.print(f"[red]断开设备连接时出错: {e}[/]")


@cli.command()
@click.option("--device", "-d", help="设备序列号或IP地址", default=None)
@click.option("--path", help="要安装到设备上的DroidRun Portal APK的路径。如果未提供，将下载并安装最新版本的portal apk。", default=None)
@click.option(
    "--debug", is_flag=True, help="启用详细调试日志", default=False
)
@coro
async def setup(path: str | None, device: str | None, debug: bool):
    """在设备上安装并启用DroidRun Portal。"""
    try:
        if not device:
            devices = await device_manager.list_devices()
            if not devices:
                console.print("[yellow]没有连接的设备。[/]")
                return

            device = devices[0].serial
            console.print(f"[blue]使用设备:[/] {device}")

        device_obj = await device_manager.get_device(device)
        if not device_obj:
            console.print(
                f"[bold red]错误:[/] 无法获取设备 {device} 的对象"
            )
            return

        if not path:
            console.print("[bold blue]正在下载DroidRun Portal APK...[/]")
            apk_context = download_portal_apk(debug)
        else:
            console.print(f"[bold blue]使用提供的APK:[/] {path}")
            apk_context = nullcontext(path)

        with apk_context as apk_path:
            if not os.path.exists(apk_path):
                console.print(f"[bold red]错误:[/] 在 {apk_path} 找不到APK文件")
                return

            console.print(f"[bold blue]步骤 1/2: 正在安装APK:[/] {apk_path}")
            result = await device_obj.install_app(apk_path, True, True)

            if "错误" in result or "Error" in result:
                console.print(f"[bold red]安装失败:[/] {result}")
                return
            else:
                console.print(f"[bold green]安装成功![/]")

            console.print(f"[bold blue]步骤 2/2: 正在启用无障碍服务[/]")

            try:
                await enable_portal_accessibility(device_obj)

                console.print("[green]无障碍服务启用成功![/]")
                console.print(
                    "\n[bold green]设置完成![/] DroidRun Portal现已安装并准备就绪。"
                )

            except Exception as e:
                console.print(
                    f"[yellow]无法自动启用无障碍服务: {e}[/]"
                )
                console.print(
                    "[yellow]正在打开无障碍设置进行手动配置...[/]"
                )

                await device_obj.shell(
                    "am start -a android.settings.ACCESSIBILITY_SETTINGS"
                )

                console.print(
                    "\n[yellow]请在您的设备上完成以下步骤:[/]"
                )
                console.print(
                    f"1. 在无障碍服务列表中找到 [bold]{PORTAL_PACKAGE_NAME}[/]"
                )
                console.print("2. 点击服务名称")
                console.print(
                    "3. 将开关切换到 [bold]开启[/] 以启用服务"
                )
                console.print("4. 接受出现的任何权限对话框")

                console.print(
                    "\n[bold green]APK安装完成![/] 请使用上述步骤手动启用无障碍服务。"
                )

    except Exception as e:
        console.print(f"[bold red]错误:[/] {e}")

        if debug:
            import traceback

            traceback.print_exc()


@cli.command()
@click.option("--device", "-d", help="设备序列号或IP地址", default=None)
@click.option(
    "--debug", is_flag=True, help="启用详细调试日志", default=False
)
@coro
async def ping(device: str | None, debug: bool):
    """Ping设备以检查其是否就绪和可访问。"""
    try:
        device_obj = await device_manager.get_device(device)
        if not device_obj:
            console.print(f"[bold red]错误:[/] 找不到设备 {device}")
            return

        await ping_portal(device_obj, debug)
        console.print(
            "[bold green]Portal已安装且可访问。您可以开始使用了![/]"
        )
    except Exception as e:
        console.print(f"[bold red]错误:[/] {e}")
        if debug:
            import traceback

            traceback.print_exc()


if __name__ == "__main__":
    command = "打开设置应用"
    device = None
    provider = "GoogleGenAI"
    model = "models/gemini-2.5-flash"
    temperature = 0
    api_key = os.getenv("GOOGLE_API_KEY")
    steps = 15
    vision = True
    reasoning = True
    reflection = False
    tracing = True
    debug = True
    base_url = None
    api_base = None
    ios = False
    run_command(
        command=command,
        device=device,
        provider=provider,
        model=model,
        steps=steps,
        temperature=temperature,
        vision=vision,
        reasoning=reasoning,
        reflection=reflection,
        tracing=tracing,
        debug=debug,
        base_url=base_url,
        api_base=api_base,
        api_key=api_key,
        ios=ios,
    )
