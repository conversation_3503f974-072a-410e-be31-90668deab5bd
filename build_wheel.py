#!/usr/bin/env python3
"""
DroidRun 本地打包脚本
专为本地开发和测试设计，生成标准的 Python 包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def backup_and_switch_config():
    """备份当前配置并切换到打包配置"""
    print("🔄 配置文件管理...")

    # 检查打包配置文件是否存在
    if not os.path.exists("pyproject_build.toml"):
        print("   ⚠️ 未找到 pyproject_build.toml，使用当前 pyproject.toml")
        return False

    # 备份当前配置
    if os.path.exists("pyproject.toml"):
        shutil.copy("pyproject.toml", "pyproject_back.toml")
        print("   ✅ 已备份当前配置为 pyproject_back.toml")

    # 切换到打包配置
    shutil.copy("pyproject_build.toml", "pyproject.toml")
    print("   ✅ 已切换到打包配置 pyproject_build.toml")

    return True

def restore_config():
    """恢复原始配置"""
    print("🔄 恢复配置文件...")

    # 如果存在备份，则恢复
    if os.path.exists("pyproject_back.toml"):
        shutil.copy("pyproject_back.toml", "pyproject.toml")
        os.remove("pyproject_back.toml")
        print("   ✅ 已恢复原始配置")
    else:
        print("   ℹ️ 未找到备份文件，保持当前配置")

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_clean = ['build', 'dist', 'droidrun.egg-info']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除: {dir_name}")

def create_wheel_manually():
    """手动创建 wheel 包"""
    print("🔨 手动创建 wheel 包...")
    
    # 创建 dist 目录
    os.makedirs('dist', exist_ok=True)
    
    # 使用 setuptools 创建基本的 wheel
    try:
        # 尝试导入必要的模块
        from setuptools import setup, find_packages
        from wheel.bdist_wheel import bdist_wheel
        
        print("✅ 找到 setuptools 和 wheel")
        
        # 创建临时 setup.py
        setup_content = '''
from setuptools import setup, find_packages

setup(
    name="droidrun",
    version="0.3.2",
    packages=find_packages(include=['droidrun', 'droidrun.*']),
    include_package_data=True,
    python_requires=">=3.10",
    install_requires=[
        "click>=8.1.0",
        "rich>=13.0.0",
        "pydantic>=2.0.0",
        "aiofiles>=23.0.0",
        "pillow>=10.0.0",
        "python-dotenv>=1.0.0",
        "typing_extensions",
    ],
    entry_points={
        "console_scripts": [
            "droidrun=droidrun.cli.main:cli",
        ],
    },
)
'''
        
        with open('temp_setup.py', 'w', encoding='utf-8') as f:
            f.write(setup_content)
        
        # 运行构建
        result = subprocess.run([
            sys.executable, 'temp_setup.py', 'bdist_wheel'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Wheel 构建成功")
            # 清理临时文件
            os.remove('temp_setup.py')
            return True
        else:
            print(f"❌ Wheel 构建失败: {result.stderr}")
            return False
            
    except ImportError as e:
        print(f"❌ 缺少必要模块: {e}")
        return False

def create_source_dist():
    """创建源码分发包"""
    print("📦 创建源码分发包...")
    
    import tarfile
    
    # 创建 tar.gz 文件
    with tarfile.open('dist/droidrun-0.3.2.tar.gz', 'w:gz') as tar:
        # 添加 droidrun 目录
        tar.add('droidrun', arcname='droidrun-0.3.2/droidrun')
        
        # 添加必要文件
        files_to_include = [
            'README.md',
            'LICENSE',
            'pyproject.toml',
            'setup.py',
            'MANIFEST.in'
        ]
        
        for file in files_to_include:
            if os.path.exists(file):
                tar.add(file, arcname=f'droidrun-0.3.2/{file}')
    
    print("✅ 源码分发包创建成功")

def check_packages():
    """检查生成的包"""
    print("🔍 检查生成的包...")
    
    dist_files = list(Path('dist').glob('*'))
    if not dist_files:
        print("❌ 没有找到包文件")
        return False
    
    print("📦 生成的包文件:")
    for file in dist_files:
        size = file.stat().st_size / 1024  # KB
        print(f"   - {file.name} ({size:.1f} KB)")
    
    return True

def main():
    """主函数"""
    print("🎯 DroidRun 本地打包工具")
    print("=" * 50)

    # 配置文件切换标志
    config_switched = False

    try:
        # 切换到打包配置
        config_switched = backup_and_switch_config()

        # 清理旧文件
        clean_build()

        # 创建包
        wheel_success = create_wheel_manually()
        create_source_dist()

        # 检查结果
        if check_packages():
            print("\n✅ 打包完成！")
            print("\n� 生成的文件:")
            print("   • droidrun-0.3.2-py3-none-any.whl (推荐安装)")
            print("   • droidrun-0.3.2.tar.gz (源码包)")
            print("\n🚀 安装命令:")
            print("   pip install dist/droidrun-0.3.2-py3-none-any.whl --force-reinstall")
            print("\n� 验证命令:")
            print("   pip show droidrun")
            print("   python -c \"import droidrun; print(f'✅ DroidRun {droidrun.__version__} 安装成功！')\"")
        else:
            print("\n❌ 打包失败")

    except Exception as e:
        print(f"\n❌ 打包过程中出现错误: {e}")

    finally:
        # 无论成功失败，都要恢复配置
        if config_switched:
            restore_config()

if __name__ == "__main__":
    main()
