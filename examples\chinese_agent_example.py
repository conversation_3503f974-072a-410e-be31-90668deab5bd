#!/usr/bin/env python3
"""
DroidRun中文AI代理使用示例

此示例展示如何配置DroidRun使用中文进行思考和回复。
"""

import asyncio
import os
from droidrun import AdbTools, DroidAgent
from droidrun.config.language_config import set_language, Language
from llama_index.llms.openai import OpenAI

async def main():
    """主函数 - 演示中文AI代理的使用"""
    
    # 设置语言为中文
    print("🌏 设置AI代理语言为中文...")
    set_language(Language.CHINESE)
    
    # 初始化LLM
    print("🧠 初始化语言模型...")
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 错误：请设置OPENAI_API_KEY环境变量")
        return
    
    llm = OpenAI(
        model="gpt-4o", 
        api_key=api_key,
        temperature=0.1
    )
    
    # 加载Android工具
    print("📱 连接Android设备...")
    try:
        tools = AdbTools()
        print("✅ 设备连接成功")
    except Exception as e:
        print(f"❌ 设备连接失败: {e}")
        return
    
    # 创建中文AI代理
    print("🤖 创建中文AI代理...")
    agent = DroidAgent(
        goal="打开设置应用并检查Android版本信息",
        llm=llm,
        tools=tools,
        max_steps=10,
        vision=True,
        reasoning=True,
        debug=True
    )
    
    print("🚀 开始执行任务...")
    print("=" * 50)
    
    try:
        # 执行任务
        result = await agent.run()
        
        print("=" * 50)
        print("✅ 任务执行完成！")
        print(f"📋 执行结果: {result}")
        
        # 显示代理记忆的信息
        if hasattr(tools, 'memory') and tools.memory:
            print("\n🧠 代理记忆的信息:")
            for i, memory in enumerate(tools.memory, 1):
                print(f"  {i}. {memory}")
        
    except Exception as e:
        print(f"❌ 任务执行失败: {e}")

async def advanced_example():
    """高级示例 - 更复杂的任务"""
    
    # 设置语言为中文
    set_language(Language.CHINESE)
    
    # 初始化组件
    llm = OpenAI(model="gpt-4o", api_key=os.getenv("OPENAI_API_KEY"))
    tools = AdbTools()
    
    # 创建高级配置的代理
    agent = DroidAgent(
        goal="打开微信应用，查看最新消息，然后返回主屏幕",
        llm=llm,
        tools=tools,
        max_steps=20,
        timeout=1800,  # 30分钟超时
        vision=True,
        reasoning=True,
        reflection=True,  # 启用反思机制
        debug=True,
        save_trajectories=True  # 保存执行轨迹
    )
    
    print("🚀 执行高级任务...")
    result = await agent.run()
    print(f"📋 任务结果: {result}")

def switch_to_english_example():
    """切换到英文模式的示例"""
    
    print("🌍 切换AI代理语言为英文...")
    set_language(Language.ENGLISH)
    
    # 现在AI代理将使用英文进行思考和回复
    print("✅ 语言已切换为英文模式")
    
    # 后续创建的代理将使用英文提示词

if __name__ == "__main__":
    print("🎯 DroidRun中文AI代理示例")
    print("=" * 50)
    
    # 运行基础示例
    asyncio.run(main())
    
    print("\n" + "=" * 50)
    print("🔄 语言切换示例")
    switch_to_english_example()
    
    print("\n💡 提示:")
    print("- 使用 set_language(Language.CHINESE) 设置中文模式")
    print("- 使用 set_language(Language.ENGLISH) 设置英文模式")
    print("- 语言设置会影响所有新创建的AI代理")
    print("- 确保在创建DroidAgent之前设置语言")
