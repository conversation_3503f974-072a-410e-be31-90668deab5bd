from dataclasses import dataclass
from typing import Optional

@dataclass
class Reflection:
    """表示对情节记忆进行反思分析的结果。"""
    goal_achieved: bool
    summary: str
    advice: Optional[str] = None
    raw_response: Optional[str] = None

    @classmethod
    def from_dict(cls, data: dict) -> 'Reflection':
        """从字典创建Reflection（例如，解析的JSON）。"""
        return cls(
            goal_achieved=data.get('goal_achieved', False),
            summary=data.get('summary', ''),
            advice=data.get('advice'),
            raw_response=data.get('raw_response')
        )