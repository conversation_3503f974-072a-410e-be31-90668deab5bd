[project]
name = "droidrun"
version = "0.3.2"
description = "A framework for controlling Android devices through LLM agents"
authors = [
    {name = "DroidRun Team", email = "<EMAIL>"},
]
dependencies = [
    "click>=8.1.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "typing_extensions",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Testing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

[project.urls]
Homepage = "https://github.com/droidrun/droidrun"
"Bug Tracker" = "https://github.com/droidrun/droidrun/issues"

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
]
full = [
    "openai>=1.0.0",
    "anthropic>=0.7.0",
    "llama-index",
    "llama-index-llms-openai",
    "llama-index-llms-google-genai",
    "llama-index-llms-anthropic",
    "llama-index-llms-ollama",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["droidrun"]

[tool.hatch.build.targets.sdist]
include = [
    "droidrun/",
    "README.md",
    "LICENSE",
    "pyproject.toml",
]
exclude = [
    "static/",
    "docs/",
    "examples/",
    "test*.py",
    "*.pyc",
    "__pycache__/",
]

[project.scripts]
droidrun = "droidrun.cli.main:cli"
